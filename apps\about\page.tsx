/**
 * About Page
 * Route: /about
 */

import { KilatCard } from '@/components/ui/card'

export default function AboutPage() {
  return (
    <div className="min-h-screen py-20 px-4">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-16 animate-kilat-fade-in">
          <h1 className="text-5xl font-bold mb-6 kilat-gradient-text">
            About Kilat.js
          </h1>
          <p className="text-xl text-glow-muted max-w-2xl mx-auto">
            A modern fullstack framework designed for speed, simplicity, and developer happiness.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <KilatCard className="animate-kilat-slide-up">
            <div className="p-8">
              <h2 className="text-2xl font-semibold mb-4 text-glow-primary">Our Mission</h2>
              <p className="text-glow-muted leading-relaxed">
                To create a framework that doesn't compromise on performance or developer experience. 
                Kilat.js is built from the ground up to be fast, reliable, and enjoyable to work with.
              </p>
            </div>
          </KilatCard>

          <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.1s' }}>
            <div className="p-8">
              <h2 className="text-2xl font-semibold mb-4 text-glow-secondary">Core Principles</h2>
              <ul className="text-glow-muted space-y-2">
                <li>• Zero external dependencies</li>
                <li>• Lightning-fast performance</li>
                <li>• Developer-first experience</li>
                <li>• Modern web standards</li>
              </ul>
            </div>
          </KilatCard>
        </div>

        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12 animate-kilat-slide-up">
            Framework Architecture
          </h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <KilatCard className="animate-kilat-scale-in">
              <div className="p-6 text-center">
                <div className="w-16 h-16 bg-glow-primary rounded-full mx-auto mb-4 flex items-center justify-center text-2xl">
                  🚀
                </div>
                <h3 className="text-lg font-semibold mb-2">SpeedRun Runtime</h3>
                <p className="text-sm text-glow-muted">
                  Native Bun.js runtime with Node.js fallback for maximum performance
                </p>
              </div>
            </KilatCard>

            <KilatCard className="animate-kilat-scale-in" style={{ animationDelay: '0.1s' }}>
              <div className="p-6 text-center">
                <div className="w-16 h-16 bg-glow-secondary rounded-full mx-auto mb-4 flex items-center justify-center text-2xl">
                  🗂️
                </div>
                <h3 className="text-lg font-semibold mb-2">App Mapping</h3>
                <p className="text-sm text-glow-muted">
                  File-based routing system with automatic layout and API handling
                </p>
              </div>
            </KilatCard>

            <KilatCard className="animate-kilat-scale-in" style={{ animationDelay: '0.2s' }}>
              <div className="p-6 text-center">
                <div className="w-16 h-16 bg-glow-accent rounded-full mx-auto mb-4 flex items-center justify-center text-2xl">
                  📦
                </div>
                <h3 className="text-lg font-semibold mb-2">KilatPack</h3>
                <p className="text-sm text-glow-muted">
                  Internal build engine with zero external bundler dependencies
                </p>
              </div>
            </KilatCard>
          </div>
        </div>

        <div className="text-center">
          <KilatCard className="inline-block animate-kilat-bounce-in">
            <div className="p-8">
              <h2 className="text-2xl font-semibold mb-4">Ready to Get Started?</h2>
              <p className="text-glow-muted mb-6">
                Join the Kilat.js community and build lightning-fast applications today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="/dashboard" 
                  className="px-6 py-3 bg-glow-primary text-white rounded-lg hover:bg-glow-primary/80 transition-colors"
                >
                  Try Dashboard
                </a>
                <a 
                  href="https://github.com/kilat-js" 
                  className="px-6 py-3 border border-glow-primary text-glow-primary rounded-lg hover:bg-glow-primary/10 transition-colors"
                >
                  View on GitHub
                </a>
              </div>
            </div>
          </KilatCard>
        </div>
      </div>
    </div>
  )
}
