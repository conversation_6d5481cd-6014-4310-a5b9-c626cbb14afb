/**
 * Users API Routes
 * Handles CRUD operations for users
 */

import { NextRequest } from 'next/server'
import { UserService } from '@/core/kilatservice/user.service'

const userService = new UserService()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    
    const result = await userService.getUsers({
      page,
      limit,
      search
    })
    
    return Response.json({
      success: true,
      data: result.users,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit)
      }
    })
    
  } catch (error) {
    console.error('Users GET Error:', error)
    return Response.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, role = 'user' } = body
    
    if (!name || !email) {
      return Response.json(
        { error: 'Name and email are required' },
        { status: 400 }
      )
    }
    
    const result = await userService.createUser({
      name,
      email,
      role
    })
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      data: result.user
    }, { status: 201 })
    
  } catch (error) {
    console.error('Users POST Error:', error)
    return Response.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('id')
    
    if (!userId) {
      return Response.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    const body = await request.json()
    const result = await userService.updateUser(userId, body)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      data: result.user
    })
    
  } catch (error) {
    console.error('Users PUT Error:', error)
    return Response.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('id')
    
    if (!userId) {
      return Response.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    const result = await userService.deleteUser(userId)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      message: 'User deleted successfully'
    })
    
  } catch (error) {
    console.error('Users DELETE Error:', error)
    return Response.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}
