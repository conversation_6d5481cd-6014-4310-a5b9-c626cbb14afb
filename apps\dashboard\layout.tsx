/**
 * Dashboard Layout - Nested layout for dashboard pages
 * This layout wraps all pages under /dashboard/*
 */

import { ReactNode } from 'react'

interface DashboardLayoutProps {
  children: ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-glow-background">
      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-glow-surface border-r border-glow-surface">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-6 text-glow-primary">Dashboard</h2>
            
            <nav className="space-y-2">
              <a 
                href="/dashboard" 
                className="block px-4 py-2 rounded-lg hover:bg-glow-primary/10 text-glow-text hover:text-glow-primary transition-colors"
              >
                📊 Overview
              </a>
              <a 
                href="/dashboard/users" 
                className="block px-4 py-2 rounded-lg hover:bg-glow-primary/10 text-glow-text hover:text-glow-primary transition-colors"
              >
                👥 Users
              </a>
              <a 
                href="/dashboard/settings" 
                className="block px-4 py-2 rounded-lg hover:bg-glow-primary/10 text-glow-text hover:text-glow-primary transition-colors"
              >
                ⚙️ Settings
              </a>
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-8">
          {children}
        </main>
      </div>
    </div>
  )
}
