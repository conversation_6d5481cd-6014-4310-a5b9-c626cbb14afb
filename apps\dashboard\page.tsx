/**
 * Dashboard Overview Page
 * Route: /dashboard
 */

import { KilatCard } from '@/components/ui/card'

export default function DashboardPage() {
  return (
    <div className="space-y-8">
      <div className="animate-kilat-fade-in">
        <h1 className="text-3xl font-bold mb-2">Dashboard Overview</h1>
        <p className="text-glow-muted">Welcome to your Kilat.js dashboard</p>
      </div>

      {/* Stats Grid */}
      <div className="grid md:grid-cols-4 gap-6">
        <KilatCard className="animate-kilat-slide-up">
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-glow-muted">Total Users</p>
                <p className="text-2xl font-bold text-glow-primary">1,234</p>
              </div>
              <div className="w-12 h-12 bg-glow-primary/20 rounded-lg flex items-center justify-center">
                👥
              </div>
            </div>
          </div>
        </KilatCard>

        <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.1s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-glow-muted">Active Sessions</p>
                <p className="text-2xl font-bold text-glow-secondary">567</p>
              </div>
              <div className="w-12 h-12 bg-glow-secondary/20 rounded-lg flex items-center justify-center">
                🔥
              </div>
            </div>
          </div>
        </KilatCard>

        <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.2s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-glow-muted">API Requests</p>
                <p className="text-2xl font-bold text-glow-accent">89.2K</p>
              </div>
              <div className="w-12 h-12 bg-glow-accent/20 rounded-lg flex items-center justify-center">
                📡
              </div>
            </div>
          </div>
        </KilatCard>

        <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.3s' }}>
          <div className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-glow-muted">Performance</p>
                <p className="text-2xl font-bold text-green-400">99.9%</p>
              </div>
              <div className="w-12 h-12 bg-green-400/20 rounded-lg flex items-center justify-center">
                ⚡
              </div>
            </div>
          </div>
        </KilatCard>
      </div>

      {/* Recent Activity */}
      <div className="grid md:grid-cols-2 gap-8">
        <KilatCard className="animate-kilat-scale-in">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-glow-primary rounded-full"></div>
                <span className="text-sm text-glow-muted">New user registered</span>
                <span className="text-xs text-glow-muted ml-auto">2 min ago</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-glow-secondary rounded-full"></div>
                <span className="text-sm text-glow-muted">API endpoint updated</span>
                <span className="text-xs text-glow-muted ml-auto">5 min ago</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-glow-accent rounded-full"></div>
                <span className="text-sm text-glow-muted">Database backup completed</span>
                <span className="text-xs text-glow-muted ml-auto">1 hour ago</span>
              </div>
            </div>
          </div>
        </KilatCard>

        <KilatCard className="animate-kilat-scale-in" style={{ animationDelay: '0.1s' }}>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">System Status</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">SpeedRun Runtime</span>
                <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Online</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">KilatPack Build</span>
                <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Ready</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Database</span>
                <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded">Connected</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Cache Layer</span>
                <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded">Warming</span>
              </div>
            </div>
          </div>
        </KilatCard>
      </div>
    </div>
  )
}
