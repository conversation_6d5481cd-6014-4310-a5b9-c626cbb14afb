/**
 * Global Layout - Root layout for all pages
 * This layout wraps all pages in the application
 */

import { ReactNode } from 'react'
import { KilatMeta } from '@/core/kilatmeta'
import { KilatCSS } from '@/core/kilatcss'

interface RootLayoutProps {
  children: ReactNode
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" className="kilat-theme-glow">
      <head>
        <KilatMeta />
        <KilatCSS />
      </head>
      <body className="min-h-screen bg-glow-background text-glow-text antialiased">
        {/* Global Navigation */}
        <nav className="border-b border-glow-surface bg-glow-surface/50 backdrop-blur-md">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-glow-primary to-glow-secondary"></div>
                <span className="text-xl font-bold kilat-gradient-text">Kilat.js</span>
              </div>
              
              <div className="hidden md:flex items-center space-x-6">
                <a href="/" className="hover:text-glow-primary transition-colors">Home</a>
                <a href="/about" className="hover:text-glow-primary transition-colors">About</a>
                <a href="/dashboard" className="hover:text-glow-primary transition-colors">Dashboard</a>
              </div>
              
              <div className="flex items-center space-x-4">
                <button className="px-4 py-2 rounded-lg bg-glow-primary text-white hover:bg-glow-primary/80 transition-colors">
                  Get Started
                </button>
              </div>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1">
          {children}
        </main>

        {/* Global Footer */}
        <footer className="border-t border-glow-surface bg-glow-surface/30 mt-auto">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center text-glow-muted">
              <p>&copy; 2025 Kilat.js. Built with ⚡ lightning speed.</p>
            </div>
          </div>
        </footer>
      </body>
    </html>
  )
}
