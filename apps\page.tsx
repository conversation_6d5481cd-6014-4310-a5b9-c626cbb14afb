/**
 * Homepage - Main landing page
 * Route: /
 */

import { KilatButton } from '@/components/ui/button'
import { KilatCard } from '@/components/ui/card'

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="animate-kilat-fade-in">
            <h1 className="text-6xl md:text-8xl font-bold mb-6 kilat-gradient-text">
              Kilat.js
            </h1>
            <p className="text-xl md:text-2xl text-glow-muted mb-8 max-w-3xl mx-auto">
              Modern fullstack framework built from scratch. 
              <span className="text-glow-primary"> Lightning fast</span>, 
              <span className="text-glow-secondary"> standalone</span>, and 
              <span className="text-glow-accent"> developer-friendly</span>.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <KilatButton 
                variant="primary" 
                size="lg"
                className="animate-kilat-scale-in"
              >
                Get Started
              </KilatButton>
              <KilatButton 
                variant="outline" 
                size="lg"
                className="animate-kilat-scale-in"
                style={{ animationDelay: '0.1s' }}
              >
                View Docs
              </KilatButton>
            </div>
          </div>
        </div>
        
        {/* Background Effects */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-glow-primary/20 rounded-full blur-3xl animate-kilat-float"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-glow-secondary/20 rounded-full blur-3xl animate-kilat-float" style={{ animationDelay: '1s' }}></div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <h2 className="text-4xl font-bold text-center mb-12 animate-kilat-slide-up">
            Why Choose Kilat.js?
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.1s' }}>
              <div className="p-6">
                <div className="w-12 h-12 bg-glow-primary rounded-lg mb-4 flex items-center justify-center">
                  ⚡
                </div>
                <h3 className="text-xl font-semibold mb-3">Lightning Fast</h3>
                <p className="text-glow-muted">
                  Built on Bun.js with native HTTP server. No Express, no Vite dependencies. 
                  Pure speed from the ground up.
                </p>
              </div>
            </KilatCard>
            
            <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.2s' }}>
              <div className="p-6">
                <div className="w-12 h-12 bg-glow-secondary rounded-lg mb-4 flex items-center justify-center">
                  🏗️
                </div>
                <h3 className="text-xl font-semibold mb-3">Standalone</h3>
                <p className="text-glow-muted">
                  Complete framework with built-in routing, state management, ORM, 
                  and build tools. No external dependencies.
                </p>
              </div>
            </KilatCard>
            
            <KilatCard className="animate-kilat-slide-up" style={{ animationDelay: '0.3s' }}>
              <div className="p-6">
                <div className="w-12 h-12 bg-glow-accent rounded-lg mb-4 flex items-center justify-center">
                  🎨
                </div>
                <h3 className="text-xl font-semibold mb-3">Developer Experience</h3>
                <p className="text-glow-muted">
                  File-based routing, hot reload, built-in themes, and powerful CLI tools. 
                  Everything you need for modern development.
                </p>
              </div>
            </KilatCard>
          </div>
        </div>
      </section>

      {/* Quick Start Section */}
      <section className="py-20 px-4 bg-glow-surface/30">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold mb-8 animate-kilat-slide-up">
            Quick Start
          </h2>
          
          <div className="max-w-2xl mx-auto">
            <div className="bg-glow-surface rounded-lg p-6 text-left animate-kilat-scale-in">
              <pre className="text-glow-accent font-kilat-mono">
{`# Create new Kilat.js app
npm create kilat-app my-app

# Navigate to project
cd my-app

# Start development server
bun dev`}
              </pre>
            </div>
            
            <p className="mt-6 text-glow-muted">
              Get started in seconds with our CLI tool. No complex setup required.
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}
