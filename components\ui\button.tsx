/**
 * KilatButton - Reusable button component with Kilat.js styling
 */

import { ReactNode, ButtonHTMLAttributes } from 'react'
import { cn } from '@/core/kilatlib/utils'

interface KilatButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const buttonVariants = {
  primary: 'bg-glow-primary text-white hover:bg-glow-primary/80 kilat-glow',
  secondary: 'bg-glow-secondary text-white hover:bg-glow-secondary/80',
  outline: 'border border-glow-primary text-glow-primary hover:bg-glow-primary/10',
  ghost: 'text-glow-text hover:bg-glow-surface/50',
}

const buttonSizes = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2',
  lg: 'px-6 py-3 text-lg',
}

export function KilatButton({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  className,
  ...props 
}: <PERSON><PERSON>ButtonProps) {
  return (
    <button
      className={cn(
        'rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-glow-primary/50',
        buttonVariants[variant],
        buttonSizes[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  )
}
