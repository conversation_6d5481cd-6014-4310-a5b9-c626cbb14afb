/**
 * KilatCard - Reusable card component with Kilat.js styling
 * Modern card component with theme support and animations
 */

import { ReactNode, HTMLAttributes, forwardRef } from 'react'
import { cn } from '@/core/kilatlib/utils'

interface KilatCardProps extends HTMLAttributes<HTMLDivElement> {
  children?: ReactNode
  variant?: 'default' | 'glass' | 'glow' | 'gradient'
  size?: 'sm' | 'md' | 'lg'
  hover?: boolean
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
}

export const KilatCard = forwardRef<HTMLDivElement, KilatCardProps>(
  ({
    children,
    className,
    variant = 'default',
    size = 'md',
    hover = false,
    shadow = 'md',
    ...props
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          // Base styles
          'kilat-card rounded-lg transition-all duration-300',

          // Variant styles
          {
            // Default card
            'bg-glow-surface border border-glow-border': variant === 'default',

            // Glass morphism card
            'bg-white/10 backdrop-blur-md border border-white/20': variant === 'glass',

            // Glowing card
            'bg-glow-surface border border-glow-primary/20 shadow-glow': variant === 'glow',

            // Gradient card
            'bg-gradient-to-br from-glow-primary/10 to-glow-secondary/10 border border-glow-primary/20': variant === 'gradient',
          },

          // Size styles
          {
            'p-3': size === 'sm',
            'p-4': size === 'md',
            'p-6': size === 'lg',
          },

          // Shadow styles
          {
            'shadow-none': shadow === 'none',
            'shadow-sm': shadow === 'sm',
            'shadow-md': shadow === 'md',
            'shadow-lg': shadow === 'lg',
            'shadow-xl': shadow === 'xl',
          },

          // Hover effects
          {
            'hover:shadow-lg hover:scale-[1.02] cursor-pointer': hover,
          },

          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

KilatCard.displayName = 'KilatCard'
