/**
 * KilatInput - Reusable input component
 * Modern input component with validation and theming support
 */

import { InputHTMLAttributes, forwardRef, ReactNode } from 'react'
import { cn } from '@/core/kilatlib/utils'

interface KilatInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: ReactNode
  rightIcon?: ReactNode
  variant?: 'default' | 'filled' | 'outline'
  inputSize?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
}

export const KilatInput = forwardRef<HTMLInputElement, KilatInputProps>(
  ({ 
    className,
    label,
    error,
    helperText,
    leftIcon,
    rightIcon,
    variant = 'default',
    inputSize = 'md',
    fullWidth = false,
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`
    const hasError = !!error
    
    return (
      <div className={cn('kilat-input-wrapper', fullWidth && 'w-full')}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-medium text-glow-text mb-2"
          >
            {label}
          </label>
        )}
        
        {/* Input container */}
        <div className="relative">
          {/* Left icon */}
          {leftIcon && (
            <div className={cn(
              'absolute left-0 top-0 h-full flex items-center justify-center text-glow-muted',
              {
                'w-8': inputSize === 'sm',
                'w-10': inputSize === 'md',
                'w-12': inputSize === 'lg',
              }
            )}>
              {leftIcon}
            </div>
          )}
          
          {/* Input field */}
          <input
            ref={ref}
            id={inputId}
            className={cn(
              // Base styles
              'kilat-input w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1',
              
              // Variant styles
              {
                // Default variant
                'bg-glow-background border-glow-border text-glow-text placeholder:text-glow-muted focus:border-glow-primary focus:ring-glow-primary/20': variant === 'default',
                
                // Filled variant
                'bg-glow-surface border-transparent text-glow-text placeholder:text-glow-muted focus:border-glow-primary focus:ring-glow-primary/20': variant === 'filled',
                
                // Outline variant
                'bg-transparent border-glow-border text-glow-text placeholder:text-glow-muted focus:border-glow-primary focus:ring-glow-primary/20': variant === 'outline',
              },
              
              // Size styles
              {
                'px-3 py-1.5 text-sm': inputSize === 'sm',
                'px-4 py-2 text-sm': inputSize === 'md',
                'px-5 py-3 text-base': inputSize === 'lg',
              },
              
              // Icon padding
              {
                'pl-8': leftIcon && inputSize === 'sm',
                'pl-10': leftIcon && inputSize === 'md',
                'pl-12': leftIcon && inputSize === 'lg',
                'pr-8': rightIcon && inputSize === 'sm',
                'pr-10': rightIcon && inputSize === 'md',
                'pr-12': rightIcon && inputSize === 'lg',
              },
              
              // Error state
              {
                'border-red-500 focus:border-red-500 focus:ring-red-500/20': hasError,
              },
              
              // Disabled state
              {
                'opacity-50 cursor-not-allowed': props.disabled,
              },
              
              className
            )}
            {...props}
          />
          
          {/* Right icon */}
          {rightIcon && (
            <div className={cn(
              'absolute right-0 top-0 h-full flex items-center justify-center text-glow-muted',
              {
                'w-8': inputSize === 'sm',
                'w-10': inputSize === 'md',
                'w-12': inputSize === 'lg',
              }
            )}>
              {rightIcon}
            </div>
          )}
        </div>
        
        {/* Helper text or error */}
        {(helperText || error) && (
          <p className={cn(
            'mt-1 text-xs',
            hasError ? 'text-red-500' : 'text-glow-muted'
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    )
  }
)

KilatInput.displayName = 'KilatInput'

/**
 * KilatTextarea - Textarea component
 */
interface KilatTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helperText?: string
  variant?: 'default' | 'filled' | 'outline'
  inputSize?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  resize?: 'none' | 'vertical' | 'horizontal' | 'both'
}

export const KilatTextarea = forwardRef<HTMLTextAreaElement, KilatTextareaProps>(
  ({ 
    className,
    label,
    error,
    helperText,
    variant = 'default',
    inputSize = 'md',
    fullWidth = false,
    resize = 'vertical',
    id,
    ...props 
  }, ref) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`
    const hasError = !!error
    
    return (
      <div className={cn('kilat-textarea-wrapper', fullWidth && 'w-full')}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={textareaId}
            className="block text-sm font-medium text-glow-text mb-2"
          >
            {label}
          </label>
        )}
        
        {/* Textarea */}
        <textarea
          ref={ref}
          id={textareaId}
          className={cn(
            // Base styles
            'kilat-textarea w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1',
            
            // Variant styles
            {
              // Default variant
              'bg-glow-background border-glow-border text-glow-text placeholder:text-glow-muted focus:border-glow-primary focus:ring-glow-primary/20': variant === 'default',
              
              // Filled variant
              'bg-glow-surface border-transparent text-glow-text placeholder:text-glow-muted focus:border-glow-primary focus:ring-glow-primary/20': variant === 'filled',
              
              // Outline variant
              'bg-transparent border-glow-border text-glow-text placeholder:text-glow-muted focus:border-glow-primary focus:ring-glow-primary/20': variant === 'outline',
            },
            
            // Size styles
            {
              'px-3 py-1.5 text-sm': inputSize === 'sm',
              'px-4 py-2 text-sm': inputSize === 'md',
              'px-5 py-3 text-base': inputSize === 'lg',
            },
            
            // Resize styles
            {
              'resize-none': resize === 'none',
              'resize-y': resize === 'vertical',
              'resize-x': resize === 'horizontal',
              'resize': resize === 'both',
            },
            
            // Error state
            {
              'border-red-500 focus:border-red-500 focus:ring-red-500/20': hasError,
            },
            
            // Disabled state
            {
              'opacity-50 cursor-not-allowed': props.disabled,
            },
            
            className
          )}
          {...props}
        />
        
        {/* Helper text or error */}
        {(helperText || error) && (
          <p className={cn(
            'mt-1 text-xs',
            hasError ? 'text-red-500' : 'text-glow-muted'
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    )
  }
)

KilatTextarea.displayName = 'KilatTextarea'

/**
 * KilatSelect - Select component
 */
interface KilatSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string
  error?: string
  helperText?: string
  variant?: 'default' | 'filled' | 'outline'
  inputSize?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  placeholder?: string
}

export const KilatSelect = forwardRef<HTMLSelectElement, KilatSelectProps>(
  ({ 
    className,
    label,
    error,
    helperText,
    variant = 'default',
    inputSize = 'md',
    fullWidth = false,
    placeholder,
    children,
    id,
    ...props 
  }, ref) => {
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`
    const hasError = !!error
    
    return (
      <div className={cn('kilat-select-wrapper', fullWidth && 'w-full')}>
        {/* Label */}
        {label && (
          <label 
            htmlFor={selectId}
            className="block text-sm font-medium text-glow-text mb-2"
          >
            {label}
          </label>
        )}
        
        {/* Select container */}
        <div className="relative">
          <select
            ref={ref}
            id={selectId}
            className={cn(
              // Base styles
              'kilat-select w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 appearance-none cursor-pointer',
              
              // Variant styles
              {
                // Default variant
                'bg-glow-background border-glow-border text-glow-text focus:border-glow-primary focus:ring-glow-primary/20': variant === 'default',
                
                // Filled variant
                'bg-glow-surface border-transparent text-glow-text focus:border-glow-primary focus:ring-glow-primary/20': variant === 'filled',
                
                // Outline variant
                'bg-transparent border-glow-border text-glow-text focus:border-glow-primary focus:ring-glow-primary/20': variant === 'outline',
              },
              
              // Size styles
              {
                'px-3 py-1.5 pr-8 text-sm': inputSize === 'sm',
                'px-4 py-2 pr-10 text-sm': inputSize === 'md',
                'px-5 py-3 pr-12 text-base': inputSize === 'lg',
              },
              
              // Error state
              {
                'border-red-500 focus:border-red-500 focus:ring-red-500/20': hasError,
              },
              
              // Disabled state
              {
                'opacity-50 cursor-not-allowed': props.disabled,
              },
              
              className
            )}
            {...props}
          >
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {children}
          </select>
          
          {/* Dropdown arrow */}
          <div className={cn(
            'absolute right-0 top-0 h-full flex items-center justify-center pointer-events-none text-glow-muted',
            {
              'w-8': inputSize === 'sm',
              'w-10': inputSize === 'md',
              'w-12': inputSize === 'lg',
            }
          )}>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
        
        {/* Helper text or error */}
        {(helperText || error) && (
          <p className={cn(
            'mt-1 text-xs',
            hasError ? 'text-red-500' : 'text-glow-muted'
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    )
  }
)

KilatSelect.displayName = 'KilatSelect'

// Export all input components
export {
  KilatInput as Input,
  KilatTextarea as Textarea,
  KilatSelect as Select,
}
