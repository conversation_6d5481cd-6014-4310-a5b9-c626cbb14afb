/**
 * KilatCache - Caching system for Kilat.js
 * Supports memory cache, Redis, ISR, and SWR patterns
 */

import { CacheEntry } from '../kilatcore/types'

interface CacheOptions {
  ttl?: number // Time to live in seconds
  tags?: string[] // Cache tags for invalidation
  revalidate?: number // ISR revalidation time
  staleWhileRevalidate?: boolean // SWR pattern
}

interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  size: number
}

export class KilatCache {
  private memoryCache: Map<string, CacheEntry> = new Map()
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    size: 0
  }
  private cleanupInterval: NodeJS.Timeout | null = null
  
  constructor() {
    // Start cleanup interval for expired entries
    this.startCleanup()
  }
  
  /**
   * Get value from cache
   */
  async get<T = any>(key: string): Promise<T | null> {
    const entry = this.memoryCache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }
    
    // Check if entry is expired
    if (this.isExpired(entry)) {
      this.memoryCache.delete(key)
      this.stats.misses++
      return null
    }
    
    this.stats.hits++
    return entry.value as T
  }
  
  /**
   * Set value in cache
   */
  async set<T = any>(key: string, value: T, ttl?: number, options: CacheOptions = {}): Promise<void> {
    const now = Date.now()
    const expiresAt = ttl ? now + (ttl * 1000) : 0
    
    const entry: CacheEntry = {
      value,
      expires: expiresAt,
      tags: options.tags || []
    }
    
    this.memoryCache.set(key, entry)
    this.stats.sets++
    this.updateSize()
    
    console.log(`💾 KilatCache: Set ${key} (TTL: ${ttl || 'none'})`)
  }
  
  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<boolean> {
    const deleted = this.memoryCache.delete(key)
    if (deleted) {
      this.stats.deletes++
      this.updateSize()
      console.log(`🗑️ KilatCache: Deleted ${key}`)
    }
    return deleted
  }
  
  /**
   * Check if key exists in cache
   */
  async has(key: string): Promise<boolean> {
    const entry = this.memoryCache.get(key)
    if (!entry) return false
    
    if (this.isExpired(entry)) {
      this.memoryCache.delete(key)
      return false
    }
    
    return true
  }
  
  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    this.memoryCache.clear()
    this.updateSize()
    console.log('🧹 KilatCache: Cleared all entries')
  }
  
  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: string[]): Promise<number> {
    let invalidated = 0
    
    for (const [key, entry] of this.memoryCache) {
      if (entry.tags && entry.tags.some(tag => tags.includes(tag))) {
        this.memoryCache.delete(key)
        invalidated++
      }
    }
    
    this.updateSize()
    console.log(`🏷️ KilatCache: Invalidated ${invalidated} entries by tags:`, tags)
    
    return invalidated
  }
  
  /**
   * Get or set with function (memoization pattern)
   */
  async getOrSet<T = any>(
    key: string, 
    factory: () => Promise<T> | T, 
    ttl?: number, 
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key)
    if (cached !== null) {
      return cached
    }
    
    // Generate value and cache it
    const value = await factory()
    await this.set(key, value, ttl, options)
    
    return value
  }
  
  /**
   * Stale-While-Revalidate pattern
   */
  async swr<T = any>(
    key: string,
    factory: () => Promise<T> | T,
    ttl: number = 300, // 5 minutes default
    staleTime: number = 60 // 1 minute stale time
  ): Promise<T> {
    const entry = this.memoryCache.get(key)
    const now = Date.now()
    
    // If no cache entry, fetch fresh data
    if (!entry) {
      const value = await factory()
      await this.set(key, value, ttl)
      return value
    }
    
    // If entry is fresh, return it
    const staleThreshold = entry.expires - (staleTime * 1000)
    if (now < staleThreshold) {
      this.stats.hits++
      return entry.value as T
    }
    
    // If entry is stale but not expired, return stale data and revalidate in background
    if (now < entry.expires) {
      this.stats.hits++
      
      // Revalidate in background
      setImmediate(async () => {
        try {
          const freshValue = await factory()
          await this.set(key, freshValue, ttl)
          console.log(`🔄 KilatCache: Background revalidated ${key}`)
        } catch (error) {
          console.error(`Error revalidating ${key}:`, error)
        }
      })
      
      return entry.value as T
    }
    
    // Entry is expired, fetch fresh data
    this.stats.misses++
    const value = await factory()
    await this.set(key, value, ttl)
    
    return value
  }
  
  /**
   * Incremental Static Regeneration pattern
   */
  async isr<T = any>(
    key: string,
    factory: () => Promise<T> | T,
    revalidate: number = 3600 // 1 hour default
  ): Promise<{ data: T; isStale: boolean }> {
    const entry = this.memoryCache.get(key)
    const now = Date.now()
    
    // If no cache entry, generate and cache
    if (!entry) {
      const data = await factory()
      await this.set(key, data, revalidate * 2) // Cache for 2x revalidate time
      return { data, isStale: false }
    }
    
    const revalidateThreshold = entry.expires - (revalidate * 1000)
    const isStale = now > revalidateThreshold
    
    // If data needs revalidation, trigger background regeneration
    if (isStale && now < entry.expires) {
      setImmediate(async () => {
        try {
          const freshData = await factory()
          await this.set(key, freshData, revalidate * 2)
          console.log(`🔄 KilatCache: ISR regenerated ${key}`)
        } catch (error) {
          console.error(`Error regenerating ${key}:`, error)
        }
      })
    }
    
    this.stats.hits++
    return { data: entry.value as T, isStale }
  }
  
  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }
  
  /**
   * Get cache hit ratio
   */
  getHitRatio(): number {
    const total = this.stats.hits + this.stats.misses
    return total > 0 ? this.stats.hits / total : 0
  }
  
  /**
   * Get all cache keys
   */
  getKeys(): string[] {
    return Array.from(this.memoryCache.keys())
  }
  
  /**
   * Get cache size in bytes (approximate)
   */
  getSizeBytes(): number {
    let size = 0
    for (const [key, entry] of this.memoryCache) {
      size += key.length * 2 // UTF-16 characters
      size += JSON.stringify(entry).length * 2
    }
    return size
  }
  
  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry): boolean {
    if (entry.expires === 0) return false // No expiration
    return Date.now() > entry.expires
  }
  
  /**
   * Update cache size stats
   */
  private updateSize(): void {
    this.stats.size = this.memoryCache.size
  }
  
  /**
   * Start cleanup interval for expired entries
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpired()
    }, 60000) // Cleanup every minute
  }
  
  /**
   * Cleanup expired entries
   */
  private cleanupExpired(): void {
    let cleaned = 0
    const now = Date.now()
    
    for (const [key, entry] of this.memoryCache) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key)
        cleaned++
      }
    }
    
    if (cleaned > 0) {
      this.updateSize()
      console.log(`🧹 KilatCache: Cleaned up ${cleaned} expired entries`)
    }
  }
  
  /**
   * Stop cleanup interval
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.memoryCache.clear()
  }
}

// Global cache instance
let globalCache: KilatCache | null = null

/**
 * Get or create global cache instance
 */
export function getCache(): KilatCache {
  if (!globalCache) {
    globalCache = new KilatCache()
  }
  return globalCache
}

/**
 * Cache decorator for methods
 */
export function cached(ttl?: number, options: CacheOptions = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function (...args: any[]) {
      const cache = getCache()
      const cacheKey = `${target.constructor.name}.${propertyKey}:${JSON.stringify(args)}`
      
      return cache.getOrSet(cacheKey, () => originalMethod.apply(this, args), ttl, options)
    }
    
    return descriptor
  }
}

/**
 * SWR hook for React components
 */
export function useSWR<T = any>(
  key: string,
  fetcher: () => Promise<T> | T,
  options: { ttl?: number; staleTime?: number } = {}
): { data: T | null; isLoading: boolean; error: Error | null } {
  const [data, setData] = React.useState<T | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)
  const [error, setError] = React.useState<Error | null>(null)
  
  React.useEffect(() => {
    const cache = getCache()
    
    cache.swr(key, fetcher, options.ttl, options.staleTime)
      .then(result => {
        setData(result)
        setError(null)
      })
      .catch(err => {
        setError(err)
      })
      .finally(() => {
        setIsLoading(false)
      })
  }, [key])
  
  return { data, isLoading, error }
}
