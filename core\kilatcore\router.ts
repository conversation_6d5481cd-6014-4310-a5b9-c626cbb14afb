/**
 * App Router - File-based routing system for Kilat.js
 * Handles automatic route discovery and request routing
 */

import { readdir, stat } from 'fs/promises'
import { join, extname, basename, dirname } from 'path'
import { KilatConfig, FileSystemRoute, APIRoute, RouteHandler } from './types'

export class AppRouter {
  private config: KilatConfig
  private routes: Map<string, FileSystemRoute> = new Map()
  private apiRoutes: Map<string, APIRoute> = new Map()
  private layouts: Map<string, string> = new Map()
  
  constructor(config: KilatConfig) {
    this.config = config
  }
  
  /**
   * Initialize the router by scanning the apps directory
   */
  async initialize(): Promise<void> {
    console.log('🔀 Initializing App Router...')
    
    await this.scanRoutes()
    await this.scanAPIRoutes()
    await this.scanLayouts()
    
    console.log(`✅ Found ${this.routes.size} page routes and ${this.apiRoutes.size} API routes`)
  }
  
  /**
   * Handle incoming requests
   */
  async handle(request: Request): Promise<Response> {
    const url = new URL(request.url)
    const pathname = url.pathname
    
    // Handle API routes first
    if (pathname.startsWith(this.config.apps.api.prefix)) {
      return this.handleAPIRoute(request, pathname)
    }
    
    // Handle page routes
    return this.handlePageRoute(request, pathname)
  }
  
  /**
   * Scan for page routes in the apps directory
   */
  private async scanRoutes(): Promise<void> {
    const appsDir = this.config.apps.dir
    await this.scanDirectory(appsDir, '')
  }
  
  /**
   * Recursively scan directory for routes
   */
  private async scanDirectory(dir: string, routePath: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name)
        
        if (entry.isDirectory()) {
          // Skip api directory (handled separately)
          if (entry.name === 'api') continue
          
          const newRoutePath = routePath + '/' + entry.name
          await this.scanDirectory(fullPath, newRoutePath)
        } else if (entry.isFile()) {
          const ext = extname(entry.name)
          const name = basename(entry.name, ext)
          
          // Only process supported extensions
          if (!this.config.apps.extensions.includes(ext)) continue
          
          // Skip layout files (handled separately)
          if (name === 'layout') continue
          
          if (name === 'page') {
            // This is a page route
            const route: FileSystemRoute = {
              path: routePath || '/',
              component: fullPath,
              dynamic: this.isDynamicRoute(routePath),
              params: this.extractParams(routePath)
            }
            
            this.routes.set(route.path, route)
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning directory ${dir}:`, error)
    }
  }
  
  /**
   * Scan for API routes
   */
  private async scanAPIRoutes(): Promise<void> {
    const apiDir = join(this.config.apps.dir, 'api')
    await this.scanAPIDirectory(apiDir, '')
  }
  
  /**
   * Recursively scan API directory
   */
  private async scanAPIDirectory(dir: string, routePath: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name)
        
        if (entry.isDirectory()) {
          const newRoutePath = routePath + '/' + entry.name
          await this.scanAPIDirectory(fullPath, newRoutePath)
        } else if (entry.isFile()) {
          const ext = extname(entry.name)
          const name = basename(entry.name, ext)
          
          if (name === 'route' && this.config.apps.extensions.includes(ext)) {
            const apiPath = this.config.apps.api.prefix + routePath
            
            const route: APIRoute = {
              path: apiPath,
              handler: fullPath,
              methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
            }
            
            this.apiRoutes.set(apiPath, route)
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning API directory ${dir}:`, error)
    }
  }
  
  /**
   * Scan for layout files
   */
  private async scanLayouts(): Promise<void> {
    // Global layout
    const globalLayout = this.config.apps.layouts.global
    if (globalLayout) {
      this.layouts.set('/', globalLayout)
    }
    
    // Nested layouts
    if (this.config.apps.layouts.nested) {
      await this.scanLayoutsRecursive(this.config.apps.dir, '')
    }
  }
  
  /**
   * Recursively scan for layout files
   */
  private async scanLayoutsRecursive(dir: string, routePath: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        if (entry.isDirectory() && entry.name !== 'api') {
          const fullPath = join(dir, entry.name)
          const newRoutePath = routePath + '/' + entry.name
          
          // Check for layout file in this directory
          const layoutPath = join(fullPath, 'layout.tsx')
          try {
            await stat(layoutPath)
            this.layouts.set(newRoutePath, layoutPath)
          } catch {
            // No layout file in this directory
          }
          
          await this.scanLayoutsRecursive(fullPath, newRoutePath)
        }
      }
    } catch (error) {
      console.error(`Error scanning layouts in ${dir}:`, error)
    }
  }
  
  /**
   * Handle API route requests
   */
  private async handleAPIRoute(request: Request, pathname: string): Promise<Response> {
    // Find matching API route
    const route = this.findMatchingAPIRoute(pathname)
    
    if (!route) {
      return new Response('API route not found', { status: 404 })
    }
    
    try {
      // Dynamic import the route handler
      const handler: RouteHandler = await import(route.handler)
      
      // Get the appropriate method handler
      const method = request.method as keyof RouteHandler
      const methodHandler = handler[method]
      
      if (!methodHandler) {
        return new Response(`Method ${method} not allowed`, { status: 405 })
      }
      
      // Execute the handler
      return await methodHandler(request)
      
    } catch (error) {
      console.error('API route error:', error)
      return new Response('Internal Server Error', { status: 500 })
    }
  }
  
  /**
   * Handle page route requests
   */
  private async handlePageRoute(request: Request, pathname: string): Promise<Response> {
    // Find matching page route
    const route = this.findMatchingPageRoute(pathname)
    
    if (!route) {
      return new Response('Page not found', { status: 404 })
    }
    
    try {
      // This would render the React component
      // For now, return a simple response
      return new Response(`Page: ${route.path}`, {
        headers: { 'Content-Type': 'text/html' }
      })
      
    } catch (error) {
      console.error('Page route error:', error)
      return new Response('Internal Server Error', { status: 500 })
    }
  }
  
  /**
   * Find matching API route
   */
  private findMatchingAPIRoute(pathname: string): APIRoute | undefined {
    // Exact match first
    if (this.apiRoutes.has(pathname)) {
      return this.apiRoutes.get(pathname)
    }
    
    // Dynamic route matching
    for (const [routePath, route] of this.apiRoutes) {
      if (this.matchesPattern(pathname, routePath)) {
        return route
      }
    }
    
    return undefined
  }
  
  /**
   * Find matching page route
   */
  private findMatchingPageRoute(pathname: string): FileSystemRoute | undefined {
    // Exact match first
    if (this.routes.has(pathname)) {
      return this.routes.get(pathname)
    }
    
    // Dynamic route matching
    for (const [routePath, route] of this.routes) {
      if (this.matchesPattern(pathname, routePath)) {
        return route
      }
    }
    
    return undefined
  }
  
  /**
   * Check if a route is dynamic (contains parameters)
   */
  private isDynamicRoute(path: string): boolean {
    return path.includes('[') && path.includes(']')
  }
  
  /**
   * Extract parameters from a route path
   */
  private extractParams(path: string): string[] {
    const params: string[] = []
    const segments = path.split('/')
    
    for (const segment of segments) {
      if (segment.startsWith('[') && segment.endsWith(']')) {
        params.push(segment.slice(1, -1))
      }
    }
    
    return params
  }
  
  /**
   * Check if a pathname matches a route pattern
   */
  private matchesPattern(pathname: string, pattern: string): boolean {
    const pathSegments = pathname.split('/').filter(Boolean)
    const patternSegments = pattern.split('/').filter(Boolean)
    
    if (pathSegments.length !== patternSegments.length) {
      return false
    }
    
    for (let i = 0; i < patternSegments.length; i++) {
      const patternSegment = patternSegments[i]
      const pathSegment = pathSegments[i]
      
      if (patternSegment.startsWith('[') && patternSegment.endsWith(']')) {
        // Dynamic segment - matches any value
        continue
      } else if (patternSegment !== pathSegment) {
        return false
      }
    }
    
    return true
  }
}
