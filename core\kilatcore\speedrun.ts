/**
 * SpeedRun Runtime - Core runtime engine for Kilat.js
 * Native HTTP server with Bun.js + Node.js fallback
 */

import { serve } from 'bun'
import { createServer } from 'http'
import { readFileSync } from 'fs'
import { join } from 'path'
import { KilatConfig } from './types'
import { AppRouter } from './router'
import { PluginManager } from '../kilatplugin/manager'
import { MiddlewareProcessor } from './middleware'

export class SpeedRunRuntime {
  private config: KilatConfig
  private router: AppRouter
  private pluginManager: PluginManager
  private middlewareProcessor: MiddlewareProcessor
  private server: any
  
  constructor(config: KilatConfig) {
    this.config = config
    this.router = new AppRouter(config)
    this.pluginManager = new PluginManager(config)
    this.middlewareProcessor = new MiddlewareProcessor(config)
  }
  
  /**
   * Initialize the runtime
   */
  async initialize(): Promise<void> {
    console.log('🚀 Initializing SpeedRun Runtime...')
    
    // Load plugins
    await this.pluginManager.loadPlugins()
    
    // Initialize router
    await this.router.initialize()
    
    // Initialize middleware
    await this.middlewareProcessor.initialize()
    
    console.log('✅ SpeedRun Runtime initialized')
  }
  
  /**
   * Start the server
   */
  async start(): Promise<void> {
    const { runtime } = this.config
    
    try {
      // Try Bun.js first
      if (runtime.engine === 'bun' || runtime.engine === 'auto') {
        await this.startBunServer()
        return
      }
    } catch (error) {
      console.warn('⚠️ Bun.js not available, falling back to Node.js')
    }
    
    // Fallback to Node.js
    if (runtime.engine === 'node' || runtime.engine === 'auto') {
      await this.startNodeServer()
      return
    }
    
    throw new Error('No suitable runtime engine available')
  }
  
  /**
   * Start Bun.js server
   */
  private async startBunServer(): Promise<void> {
    const { runtime } = this.config
    
    this.server = serve({
      port: runtime.port,
      hostname: runtime.host,
      fetch: this.handleRequest.bind(this),
      error: this.handleError.bind(this),
    })
    
    console.log(`⚡ SpeedRun server running on http://${runtime.host}:${runtime.port} (Bun.js)`)
  }
  
  /**
   * Start Node.js server
   */
  private async startNodeServer(): Promise<void> {
    const { runtime } = this.config
    
    this.server = createServer(async (req, res) => {
      try {
        const request = this.nodeRequestToFetch(req)
        const response = await this.handleRequest(request)
        await this.sendNodeResponse(res, response)
      } catch (error) {
        this.handleError(error)
        res.statusCode = 500
        res.end('Internal Server Error')
      }
    })
    
    this.server.listen(runtime.port, runtime.host, () => {
      console.log(`⚡ SpeedRun server running on http://${runtime.host}:${runtime.port} (Node.js)`)
    })
  }
  
  /**
   * Handle incoming requests
   */
  private async handleRequest(request: Request): Promise<Response> {
    try {
      // Process middleware
      const middlewareResult = await this.middlewareProcessor.process(request)
      if (middlewareResult) {
        return middlewareResult
      }
      
      // Route the request
      const response = await this.router.handle(request)
      
      // Add default headers
      this.addDefaultHeaders(response)
      
      return response
      
    } catch (error) {
      console.error('Request handling error:', error)
      return new Response('Internal Server Error', { status: 500 })
    }
  }
  
  /**
   * Handle errors
   */
  private handleError(error: Error): void {
    console.error('SpeedRun Runtime Error:', error)
  }
  
  /**
   * Convert Node.js request to Fetch API request
   */
  private nodeRequestToFetch(req: any): Request {
    const url = `http://${req.headers.host}${req.url}`
    const headers = new Headers()
    
    for (const [key, value] of Object.entries(req.headers)) {
      if (typeof value === 'string') {
        headers.set(key, value)
      } else if (Array.isArray(value)) {
        headers.set(key, value.join(', '))
      }
    }
    
    return new Request(url, {
      method: req.method,
      headers,
      body: req.method !== 'GET' && req.method !== 'HEAD' ? req : undefined,
    })
  }
  
  /**
   * Send response to Node.js response object
   */
  private async sendNodeResponse(res: any, response: Response): Promise<void> {
    res.statusCode = response.status
    
    // Set headers
    response.headers.forEach((value, key) => {
      res.setHeader(key, value)
    })
    
    // Send body
    if (response.body) {
      const reader = response.body.getReader()
      while (true) {
        const { done, value } = await reader.read()
        if (done) break
        res.write(value)
      }
    }
    
    res.end()
  }
  
  /**
   * Add default headers to response
   */
  private addDefaultHeaders(response: Response): void {
    if (!response.headers.has('X-Powered-By')) {
      response.headers.set('X-Powered-By', 'Kilat.js SpeedRun')
    }
    
    if (!response.headers.has('X-Runtime')) {
      response.headers.set('X-Runtime', this.config.runtime.engine)
    }
  }
  
  /**
   * Stop the server
   */
  async stop(): Promise<void> {
    if (this.server) {
      if (typeof this.server.stop === 'function') {
        // Bun.js server
        this.server.stop()
      } else {
        // Node.js server
        this.server.close()
      }
      console.log('🛑 SpeedRun server stopped')
    }
  }
  
  /**
   * Reload the server (for development)
   */
  async reload(): Promise<void> {
    console.log('🔄 Reloading SpeedRun server...')
    await this.stop()
    await this.initialize()
    await this.start()
  }
  
  /**
   * Get server status
   */
  getStatus(): any {
    return {
      runtime: this.config.runtime.engine,
      port: this.config.runtime.port,
      host: this.config.runtime.host,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version,
    }
  }
}
