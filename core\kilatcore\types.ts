/**
 * Type definitions for Kilat.js Core
 */

export interface KilatConfig {
  runtime: {
    engine: 'bun' | 'node' | 'auto'
    fallback: 'node' | 'bun'
    port: number
    host: string
    https: boolean
  }
  
  apps: {
    dir: string
    extensions: string[]
    layouts: {
      global: string
      nested: boolean
    }
    api: {
      dir: string
      prefix: string
    }
  }
  
  build: {
    outDir: string
    target: string
    minify: boolean
    sourcemap: boolean
    splitting: boolean
    treeshaking: boolean
    bundleAnalyzer: boolean
  }
  
  plugins: {
    dir: string
    autoLoad: boolean
    devPlugins: string[]
    buildPlugins: string[]
  }
  
  css: {
    framework: 'tailwind' | 'vanilla' | 'styled'
    themes: {
      default: string
      available: string[]
    }
    animations: {
      enabled: boolean
      preset: 'smooth' | 'bouncy' | 'sharp'
    }
  }
  
  state: {
    ssr: boolean
    hydration: 'full' | 'partial' | 'islands'
    persistence: 'localStorage' | 'sessionStorage' | 'none'
  }
  
  images: {
    domains: string[]
    formats: string[]
    sizes: number[]
    quality: number
  }
  
  middleware: {
    cors: {
      enabled: boolean
      origin: string
    }
    rateLimit: {
      enabled: boolean
      windowMs: number
      max: number
    }
    auth: {
      providers: any[]
    }
  }
  
  meta: {
    siteName: string
    defaultTitle: string
    titleTemplate: string
    description: string
    openGraph: {
      type: string
      locale: string
    }
  }
  
  dev: {
    hmr: boolean
    overlay: boolean
    port: number
    open: boolean
    https: boolean
  }
  
  export: {
    outDir: string
    trailingSlash: boolean
    generateSitemap: boolean
    generateRobots: boolean
  }
  
  experimental: {
    turbo: boolean
    edgeRuntime: boolean
    serverComponents: boolean
  }
}

export interface RouteHandler {
  GET?: (request: Request) => Promise<Response> | Response
  POST?: (request: Request) => Promise<Response> | Response
  PUT?: (request: Request) => Promise<Response> | Response
  DELETE?: (request: Request) => Promise<Response> | Response
  PATCH?: (request: Request) => Promise<Response> | Response
  HEAD?: (request: Request) => Promise<Response> | Response
  OPTIONS?: (request: Request) => Promise<Response> | Response
}

export interface PageComponent {
  default: React.ComponentType<any>
  metadata?: PageMetadata
  generateStaticParams?: () => Promise<any[]>
}

export interface LayoutComponent {
  default: React.ComponentType<{ children: React.ReactNode }>
  metadata?: PageMetadata
}

export interface PageMetadata {
  title?: string
  description?: string
  keywords?: string[]
  openGraph?: {
    title?: string
    description?: string
    image?: string
    type?: string
  }
  twitter?: {
    card?: string
    title?: string
    description?: string
    image?: string
  }
}

export interface Plugin {
  name: string
  setup: (build: BuildContext) => void | Promise<void>
  options?: any
}

export interface BuildContext {
  config: KilatConfig
  isDev: boolean
  isProd: boolean
  onResolve: (filter: RegExp, callback: (args: any) => any) => void
  onLoad: (filter: RegExp, callback: (args: any) => any) => void
  onTransform: (filter: RegExp, callback: (args: any) => any) => void
}

export interface MiddlewareContext {
  request: Request
  config: KilatConfig
  next: () => Promise<Response>
}

export interface CacheEntry {
  value: any
  expires: number
  tags?: string[]
}

export interface DatabaseConnection {
  connect: () => Promise<void>
  disconnect: () => Promise<void>
  query: (sql: string, params?: any[]) => Promise<any>
  transaction: (callback: (tx: any) => Promise<any>) => Promise<any>
}

export interface FileSystemRoute {
  path: string
  component: string
  layout?: string
  metadata?: PageMetadata
  params?: string[]
  dynamic?: boolean
}

export interface APIRoute {
  path: string
  handler: string
  methods: string[]
  middleware?: string[]
}

export interface StaticAsset {
  path: string
  content: Buffer
  contentType: string
  etag: string
  lastModified: Date
}

export interface HotReloadEvent {
  type: 'update' | 'add' | 'remove'
  path: string
  timestamp: number
}

export interface BuildResult {
  success: boolean
  errors: string[]
  warnings: string[]
  assets: StaticAsset[]
  duration: number
  size: number
}
