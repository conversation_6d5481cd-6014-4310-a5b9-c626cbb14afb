/**
 * KilatCSS - Global styles for Kilat.js
 * Includes Tailwind CSS base, components, and utilities
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Kilat.js Base Styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

/* Kilat.js Component Styles */
@layer components {
  /* Theme Variables */
  .kilat-theme-glow {
    --color-primary: #3b82f6;
    --color-secondary: #8b5cf6;
    --color-accent: #06b6d4;
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-text: #f8fafc;
    --color-muted: #64748b;
  }
  
  .kilat-theme-cyber {
    --color-primary: #00ff88;
    --color-secondary: #ff0080;
    --color-accent: #00d4ff;
    --color-background: #0a0a0a;
    --color-surface: #1a1a1a;
    --color-text: #ffffff;
    --color-muted: #666666;
  }
  
  .kilat-theme-pastel {
    --color-primary: #fbbf24;
    --color-secondary: #f472b6;
    --color-accent: #34d399;
    --color-background: #fef7ff;
    --color-surface: #ffffff;
    --color-text: #1f2937;
    --color-muted: #9ca3af;
  }
  
  .kilat-theme-retro {
    --color-primary: #f59e0b;
    --color-secondary: #ef4444;
    --color-accent: #10b981;
    --color-background: #292524;
    --color-surface: #44403c;
    --color-text: #fbbf24;
    --color-muted: #a8a29e;
  }
  
  /* Kilat Components */
  .kilat-container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }
  
  .kilat-card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  .kilat-button {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .kilat-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
  
  .kilat-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .kilat-button-outline {
    @apply border border-input hover:bg-accent hover:text-accent-foreground;
  }
  
  .kilat-input {
    @apply flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Navigation */
  .kilat-nav {
    @apply flex items-center justify-between p-4 border-b;
  }
  
  .kilat-nav-brand {
    @apply flex items-center space-x-2 text-xl font-bold;
  }
  
  .kilat-nav-links {
    @apply hidden md:flex items-center space-x-6;
  }
  
  .kilat-nav-link {
    @apply text-sm font-medium transition-colors hover:text-primary;
  }
  
  /* Layout */
  .kilat-sidebar {
    @apply w-64 bg-surface border-r border-border;
  }
  
  .kilat-main {
    @apply flex-1 p-8;
  }
  
  /* Effects */
  .kilat-glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .kilat-glow {
    box-shadow: 0 0 20px rgba(var(--color-primary), 0.5);
  }
  
  .kilat-cyber-glow {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
  }
  
  .kilat-gradient-text {
    background: linear-gradient(45deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Kilat.js Utility Classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  /* Responsive utilities */
  .container-xs {
    @apply max-w-xs mx-auto;
  }
  
  .container-sm {
    @apply max-w-sm mx-auto;
  }
  
  .container-md {
    @apply max-w-md mx-auto;
  }
  
  .container-lg {
    @apply max-w-lg mx-auto;
  }
  
  .container-xl {
    @apply max-w-xl mx-auto;
  }
  
  .container-2xl {
    @apply max-w-2xl mx-auto;
  }
  
  .container-3xl {
    @apply max-w-3xl mx-auto;
  }
  
  .container-4xl {
    @apply max-w-4xl mx-auto;
  }
  
  .container-5xl {
    @apply max-w-5xl mx-auto;
  }
  
  .container-6xl {
    @apply max-w-6xl mx-auto;
  }
  
  .container-7xl {
    @apply max-w-7xl mx-auto;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px currentColor;
  }
  to {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .kilat-theme-auto {
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-text: #f8fafc;
    --color-muted: #64748b;
  }
}

/* Light mode support */
@media (prefers-color-scheme: light) {
  .kilat-theme-auto {
    --color-background: #ffffff;
    --color-surface: #f8fafc;
    --color-text: #1e293b;
    --color-muted: #64748b;
  }
}

/* Print styles */
@media print {
  .kilat-no-print {
    display: none !important;
  }
  
  .kilat-print-only {
    display: block !important;
  }
}
