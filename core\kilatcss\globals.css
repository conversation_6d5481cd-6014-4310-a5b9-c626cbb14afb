/**
 * KilatCSS - Global styles for Kilat.js
 * Includes Tailwind CSS base, components, and utilities
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Kilat.js Base Styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

/* Kilat.js Component Styles */
@layer components {
  /* Theme Variables */
  .kilat-theme-glow {
    --color-primary: #60a5fa;
    --color-secondary: #a78bfa;
    --color-accent: #34d399;
    --color-background: #0a0f1c;
    --color-surface: #1e293b;
    --color-border: #334155;
    --color-text: #f1f5f9;
    --color-muted: #94a3b8;
    --glow-primary: rgba(96, 165, 250, 0.5);
    --glow-secondary: rgba(167, 139, 250, 0.5);
    --glow-accent: rgba(52, 211, 153, 0.5);
  }
  
  .kilat-theme-cyber {
    --color-primary: #00ff88;
    --color-secondary: #ff0080;
    --color-accent: #00d4ff;
    --color-background: #0a0a0a;
    --color-surface: #1a1a1a;
    --color-text: #ffffff;
    --color-muted: #666666;
  }
  
  .kilat-theme-pastel {
    --color-primary: #fbbf24;
    --color-secondary: #f472b6;
    --color-accent: #34d399;
    --color-background: #fef7ff;
    --color-surface: #ffffff;
    --color-text: #1f2937;
    --color-muted: #9ca3af;
  }
  
  .kilat-theme-retro {
    --color-primary: #f59e0b;
    --color-secondary: #ef4444;
    --color-accent: #10b981;
    --color-background: #292524;
    --color-surface: #44403c;
    --color-text: #fbbf24;
    --color-muted: #a8a29e;
  }
  
  /* Kilat Components */
  .kilat-container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }
  
  .kilat-card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }
  
  .kilat-button {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .kilat-button-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }
  
  .kilat-button-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .kilat-button-outline {
    @apply border border-input hover:bg-accent hover:text-accent-foreground;
  }
  
  .kilat-input {
    @apply flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  /* Navigation */
  .kilat-nav {
    @apply flex items-center justify-between p-4 border-b;
  }
  
  .kilat-nav-brand {
    @apply flex items-center space-x-2 text-xl font-bold;
  }
  
  .kilat-nav-links {
    @apply hidden md:flex items-center space-x-6;
  }
  
  .kilat-nav-link {
    @apply text-sm font-medium transition-colors hover:text-primary;
  }
  
  /* Layout */
  .kilat-sidebar {
    @apply w-64 bg-surface border-r border-border;
  }
  
  .kilat-main {
    @apply flex-1 p-8;
  }
  
  /* Effects */
  .kilat-glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .kilat-glow {
    box-shadow: 0 0 20px rgba(var(--color-primary), 0.5);
  }
  
  .kilat-cyber-glow {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
  }
  
  .kilat-gradient-text {
    background: linear-gradient(45deg, var(--color-primary), var(--color-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Kilat.js Utility Classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .animate-kilat-fade-in {
    animation: kilatFadeIn 0.8s ease-out;
  }

  .animate-kilat-slide-up {
    animation: kilatSlideUp 0.8s ease-out;
  }

  .animate-kilat-scale-in {
    animation: kilatScaleIn 0.6s ease-out;
  }

  .animate-kilat-glow {
    animation: kilatGlow 3s ease-in-out infinite alternate;
  }

  .animate-kilat-float {
    animation: kilatFloat 6s ease-in-out infinite;
  }

  .animate-kilat-pulse {
    animation: kilatPulse 2s ease-in-out infinite;
  }

  .animate-kilat-bounce {
    animation: kilatBounce 1s ease-in-out infinite;
  }

  .animate-kilat-spin-slow {
    animation: spin 3s linear infinite;
  }

  .animate-kilat-gradient {
    animation: kilatGradient 4s ease-in-out infinite;
  }

  /* Background utilities */
  .bg-300 {
    background-size: 300% 300%;
  }

  .bg-400 {
    background-size: 400% 400%;
  }

  /* Gradient text utilities */
  .gradient-text {
    background: linear-gradient(45deg, #60a5fa, #a78bfa, #34d399);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: kilatGradient 4s ease-in-out infinite;
  }

  /* Glass morphism */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* Responsive utilities */
  .container-xs {
    @apply max-w-xs mx-auto;
  }
  
  .container-sm {
    @apply max-w-sm mx-auto;
  }
  
  .container-md {
    @apply max-w-md mx-auto;
  }
  
  .container-lg {
    @apply max-w-lg mx-auto;
  }
  
  .container-xl {
    @apply max-w-xl mx-auto;
  }
  
  .container-2xl {
    @apply max-w-2xl mx-auto;
  }
  
  .container-3xl {
    @apply max-w-3xl mx-auto;
  }
  
  .container-4xl {
    @apply max-w-4xl mx-auto;
  }
  
  .container-5xl {
    @apply max-w-5xl mx-auto;
  }
  
  .container-6xl {
    @apply max-w-6xl mx-auto;
  }
  
  .container-7xl {
    @apply max-w-7xl mx-auto;
  }
}

/* Kilat.js Animations */
@keyframes kilatFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes kilatSlideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes kilatScaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes kilatGlow {
  from {
    box-shadow: 0 0 10px var(--glow-primary, rgba(96, 165, 250, 0.5));
  }
  to {
    box-shadow: 0 0 30px var(--glow-primary, rgba(96, 165, 250, 0.5)),
                0 0 60px var(--glow-primary, rgba(96, 165, 250, 0.3));
  }
}

@keyframes kilatFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes kilatPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes kilatBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes kilatGradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .kilat-theme-auto {
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-text: #f8fafc;
    --color-muted: #64748b;
  }
}

/* Light mode support */
@media (prefers-color-scheme: light) {
  .kilat-theme-auto {
    --color-background: #ffffff;
    --color-surface: #f8fafc;
    --color-text: #1e293b;
    --color-muted: #64748b;
  }
}

/* Print styles */
@media print {
  .kilat-no-print {
    display: none !important;
  }
  
  .kilat-print-only {
    display: block !important;
  }
}
