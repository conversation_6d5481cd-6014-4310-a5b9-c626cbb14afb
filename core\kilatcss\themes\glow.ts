/**
 * Glow Theme - Default dark theme for Kilat.js
 * Modern dark theme with blue accents and glowing effects
 */

export const glowTheme = {
  name: 'glow',
  displayName: 'Glow',
  type: 'dark',
  
  colors: {
    // Primary colors
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6', // Main primary
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554',
    },
    
    // Secondary colors
    secondary: {
      50: '#faf5ff',
      100: '#f3e8ff',
      200: '#e9d5ff',
      300: '#d8b4fe',
      400: '#c084fc',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8', // Main secondary
      900: '#581c87',
      950: '#3b0764',
    },
    
    // Accent colors
    accent: {
      50: '#ecfeff',
      100: '#cffafe',
      200: '#a5f3fc',
      300: '#67e8f9',
      400: '#22d3ee',
      500: '#06b6d4', // Main accent
      600: '#0891b2',
      700: '#0e7490',
      800: '#155e75',
      900: '#164e63',
      950: '#083344',
    },
    
    // Neutral colors
    neutral: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b', // Muted text
      600: '#475569',
      700: '#334155',
      800: '#1e293b', // Surface
      900: '#0f172a', // Background
      950: '#020617',
    },
    
    // Semantic colors
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16',
    },
    
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
      950: '#451a03',
    },
    
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a',
    },
  },
  
  // CSS custom properties
  cssVars: {
    '--kilat-primary': '#3b82f6',
    '--kilat-secondary': '#8b5cf6',
    '--kilat-accent': '#06b6d4',
    '--kilat-background': '#0f172a',
    '--kilat-surface': '#1e293b',
    '--kilat-text': '#f8fafc',
    '--kilat-muted': '#64748b',
    '--kilat-border': '#334155',
    '--kilat-success': '#22c55e',
    '--kilat-warning': '#f59e0b',
    '--kilat-error': '#ef4444',
  },
  
  // Shadows and effects
  shadows: {
    glow: '0 0 20px rgba(59, 130, 246, 0.5)',
    'glow-lg': '0 0 40px rgba(59, 130, 246, 0.6)',
    'glow-xl': '0 0 60px rgba(59, 130, 246, 0.7)',
    soft: '0 4px 20px rgba(0, 0, 0, 0.1)',
    'soft-lg': '0 8px 40px rgba(0, 0, 0, 0.15)',
    'soft-xl': '0 12px 60px rgba(0, 0, 0, 0.2)',
  },
  
  // Gradients
  gradients: {
    primary: 'linear-gradient(45deg, #3b82f6, #8b5cf6)',
    secondary: 'linear-gradient(45deg, #8b5cf6, #06b6d4)',
    accent: 'linear-gradient(45deg, #06b6d4, #22c55e)',
    hero: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
    card: 'linear-gradient(145deg, #1e293b, #0f172a)',
  },
  
  // Typography
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace'],
      display: ['Poppins', 'system-ui', 'sans-serif'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
      '6xl': '3.75rem',
      '7xl': '4.5rem',
      '8xl': '6rem',
      '9xl': '8rem',
    },
  },
  
  // Animations
  animations: {
    'fade-in': 'fadeIn 0.5s ease-out',
    'slide-up': 'slideUp 0.6s ease-out',
    'slide-down': 'slideDown 0.6s ease-out',
    'scale-in': 'scaleIn 0.4s ease-out',
    'bounce-in': 'bounceIn 0.8s ease-out',
    'glow': 'glow 2s ease-in-out infinite alternate',
    'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
    'spin-slow': 'spin 3s linear infinite',
    'float': 'float 3s ease-in-out infinite',
  },
  
  // Component styles
  components: {
    button: {
      primary: {
        background: 'var(--kilat-primary)',
        color: 'white',
        boxShadow: 'var(--kilat-glow)',
        '&:hover': {
          background: 'rgba(59, 130, 246, 0.8)',
        },
      },
      secondary: {
        background: 'var(--kilat-secondary)',
        color: 'white',
        '&:hover': {
          background: 'rgba(139, 92, 246, 0.8)',
        },
      },
      outline: {
        border: '1px solid var(--kilat-primary)',
        color: 'var(--kilat-primary)',
        background: 'transparent',
        '&:hover': {
          background: 'rgba(59, 130, 246, 0.1)',
        },
      },
    },
    
    card: {
      default: {
        background: 'var(--kilat-surface)',
        border: '1px solid var(--kilat-border)',
        borderRadius: '0.5rem',
        boxShadow: 'var(--kilat-soft)',
      },
      glass: {
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(12px)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        borderRadius: '0.5rem',
      },
      glow: {
        background: 'var(--kilat-surface)',
        border: '1px solid rgba(59, 130, 246, 0.2)',
        borderRadius: '0.5rem',
        boxShadow: 'var(--kilat-glow)',
      },
    },
  },
  
  // Responsive breakpoints
  breakpoints: {
    xs: '475px',
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
}
