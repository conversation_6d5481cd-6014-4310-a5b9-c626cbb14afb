/**
 * Error Overlay - Development error display system
 * Shows detailed error information with stack traces and source maps
 */

import { KilatConfig } from '../kilatcore/types'

export class ErrorOverlay {
  private config: KilatConfig
  
  constructor(config: KilatConfig) {
    this.config = config
  }
  
  /**
   * Render error as HTML response
   */
  async renderError(error: Error, request: Request): Promise<Response> {
    const errorHtml = this.generateErrorHTML(error, request)
    
    return new Response(errorHtml, {
      status: 500,
      headers: {
        'Content-Type': 'text/html',
        'X-Error-Overlay': 'true'
      }
    })
  }
  
  /**
   * Generate error HTML
   */
  private generateErrorHTML(error: Error, request: Request): string {
    const url = new URL(request.url)
    const stack = this.formatStackTrace(error.stack || '')
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Kilat.js Error</title>
  <style>
    ${this.getErrorStyles()}
  </style>
</head>
<body>
  <div class="error-overlay">
    <div class="error-header">
      <h1>⚡ Kilat.js Error</h1>
      <div class="error-meta">
        <span class="error-time">${new Date().toLocaleTimeString()}</span>
        <span class="error-url">${url.pathname}</span>
      </div>
    </div>
    
    <div class="error-content">
      <div class="error-message">
        <h2>${error.name}</h2>
        <p>${error.message}</p>
      </div>
      
      <div class="error-stack">
        <h3>Stack Trace</h3>
        <pre><code>${stack}</code></pre>
      </div>
      
      <div class="error-request">
        <h3>Request Details</h3>
        <div class="request-info">
          <div><strong>Method:</strong> ${request.method}</div>
          <div><strong>URL:</strong> ${url.href}</div>
          <div><strong>Headers:</strong></div>
          <pre><code>${this.formatHeaders(request.headers)}</code></pre>
        </div>
      </div>
    </div>
    
    <div class="error-footer">
      <p>This error overlay is only shown in development mode.</p>
      <button onclick="window.location.reload()">Reload Page</button>
    </div>
  </div>
</body>
</html>`
  }
  
  /**
   * Format stack trace for better readability
   */
  private formatStackTrace(stack: string): string {
    return stack
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .map(line => {
        // Highlight file paths
        if (line.includes('at ')) {
          return line.replace(
            /at (.+) \((.+):(\d+):(\d+)\)/,
            'at <span class="function">$1</span> (<span class="file">$2</span>:<span class="line">$3</span>:<span class="column">$4</span>)'
          )
        }
        return line
      })
      .join('\n')
  }
  
  /**
   * Format request headers
   */
  private formatHeaders(headers: Headers): string {
    const headerObj: Record<string, string> = {}
    headers.forEach((value, key) => {
      headerObj[key] = value
    })
    return JSON.stringify(headerObj, null, 2)
  }
  
  /**
   * Get error overlay styles
   */
  private getErrorStyles(): string {
    return `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        background: #1a1a1a;
        color: #ffffff;
        line-height: 1.6;
      }
      
      .error-overlay {
        min-height: 100vh;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
      }
      
      .error-header {
        border-bottom: 2px solid #ff4444;
        padding-bottom: 20px;
        margin-bottom: 30px;
      }
      
      .error-header h1 {
        color: #ff4444;
        font-size: 2.5rem;
        margin-bottom: 10px;
      }
      
      .error-meta {
        display: flex;
        gap: 20px;
        font-size: 0.9rem;
        color: #888;
      }
      
      .error-content {
        display: grid;
        gap: 30px;
      }
      
      .error-message h2 {
        color: #ff6666;
        font-size: 1.5rem;
        margin-bottom: 10px;
      }
      
      .error-message p {
        font-size: 1.1rem;
        color: #ffcccc;
        background: #2a1a1a;
        padding: 15px;
        border-radius: 8px;
        border-left: 4px solid #ff4444;
      }
      
      .error-stack h3,
      .error-request h3 {
        color: #66ccff;
        font-size: 1.2rem;
        margin-bottom: 15px;
      }
      
      pre {
        background: #0a0a0a;
        padding: 20px;
        border-radius: 8px;
        overflow-x: auto;
        border: 1px solid #333;
      }
      
      code {
        color: #ffffff;
        font-size: 0.9rem;
      }
      
      .function {
        color: #66ff66;
      }
      
      .file {
        color: #66ccff;
      }
      
      .line,
      .column {
        color: #ffcc66;
      }
      
      .request-info {
        background: #1a1a1a;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #333;
      }
      
      .request-info > div {
        margin-bottom: 10px;
      }
      
      .request-info strong {
        color: #66ccff;
      }
      
      .error-footer {
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #333;
        text-align: center;
        color: #888;
      }
      
      .error-footer button {
        background: #66ccff;
        color: #000;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1rem;
        margin-top: 15px;
        transition: background 0.2s;
      }
      
      .error-footer button:hover {
        background: #88ddff;
      }
      
      @media (max-width: 768px) {
        .error-overlay {
          padding: 10px;
        }
        
        .error-header h1 {
          font-size: 2rem;
        }
        
        .error-meta {
          flex-direction: column;
          gap: 5px;
        }
        
        pre {
          padding: 15px;
          font-size: 0.8rem;
        }
      }
    `
  }
  
  /**
   * Generate error JSON for API responses
   */
  renderErrorJSON(error: Error): Response {
    const errorData = {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n') || [],
        timestamp: new Date().toISOString(),
        framework: 'Kilat.js',
        development: true
      }
    }
    
    return new Response(JSON.stringify(errorData, null, 2), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'X-Error-Overlay': 'true'
      }
    })
  }
}
