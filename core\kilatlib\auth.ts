/**
 * KilatLib Auth - Authentication and authorization utilities
 * Handles JWT tokens, session management, and permission checking
 */

interface User {
  id: string
  email: string
  name: string
  role: string
  permissions: string[]
  createdAt: Date
  updatedAt: Date
}

interface Session {
  userId: string
  user: User
  role: string
  permissions: string[]
  expiresAt: Date
}

interface AuthResult {
  success: boolean
  user?: User
  session?: Session
  error?: string
}

/**
 * Check if request is authenticated
 */
export async function isAuthenticated(request: Request): Promise<Session | null> {
  try {
    // Try to get token from Authorization header
    const authHeader = request.headers.get('authorization')
    let token: string | null = null
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    }
    
    // Try to get token from cookies
    if (!token) {
      const cookieHeader = request.headers.get('cookie')
      if (cookieHeader) {
        const cookies = parseCookies(cookieHeader)
        token = cookies.auth_token || cookies.session_token
      }
    }
    
    if (!token) {
      return null
    }
    
    // Verify and decode token
    const payload = await verifyJWT(token)
    if (!payload) {
      return null
    }
    
    // Get user from database/cache
    const user = await getUserById(payload.userId)
    if (!user) {
      return null
    }
    
    // Check if session is still valid
    if (payload.exp && payload.exp < Date.now() / 1000) {
      return null
    }
    
    return {
      userId: user.id,
      user,
      role: user.role,
      permissions: user.permissions,
      expiresAt: new Date(payload.exp * 1000)
    }
    
  } catch (error) {
    console.error('Authentication error:', error)
    return null
  }
}

/**
 * Check if user has specific permission
 */
export async function hasPermission(user: User, permission: string): Promise<boolean> {
  // Admin role has all permissions
  if (user.role === 'admin') {
    return true
  }
  
  // Check specific permission
  return user.permissions.includes(permission)
}

/**
 * Check if user has any of the specified roles
 */
export function hasRole(user: User, roles: string | string[]): boolean {
  const roleArray = Array.isArray(roles) ? roles : [roles]
  return roleArray.includes(user.role)
}

/**
 * Generate JWT token for user
 */
export async function generateJWT(user: User, expiresIn: string = '7d'): Promise<string> {
  const payload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + parseExpiresIn(expiresIn)
  }
  
  // In a real implementation, you'd use a proper JWT library
  // For now, we'll create a simple base64 encoded token
  const header = { alg: 'HS256', typ: 'JWT' }
  const encodedHeader = btoa(JSON.stringify(header))
  const encodedPayload = btoa(JSON.stringify(payload))
  const signature = await createSignature(`${encodedHeader}.${encodedPayload}`)
  
  return `${encodedHeader}.${encodedPayload}.${signature}`
}

/**
 * Verify JWT token
 */
export async function verifyJWT(token: string): Promise<any | null> {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }
    
    const [header, payload, signature] = parts
    
    // Verify signature
    const expectedSignature = await createSignature(`${header}.${payload}`)
    if (signature !== expectedSignature) {
      return null
    }
    
    // Decode payload
    const decodedPayload = JSON.parse(atob(payload))
    
    return decodedPayload
    
  } catch (error) {
    console.error('JWT verification error:', error)
    return null
  }
}

/**
 * Hash password
 */
export async function hashPassword(password: string): Promise<string> {
  // In a real implementation, you'd use bcrypt or similar
  // For now, we'll use a simple hash
  const encoder = new TextEncoder()
  const data = encoder.encode(password + 'kilat_salt')
  const hashBuffer = await crypto.subtle.digest('SHA-256', data)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}

/**
 * Verify password
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  const passwordHash = await hashPassword(password)
  return passwordHash === hash
}

/**
 * Authenticate user with email and password
 */
export async function authenticateUser(email: string, password: string): Promise<AuthResult> {
  try {
    // Get user by email
    const user = await getUserByEmail(email)
    if (!user) {
      return {
        success: false,
        error: 'Invalid credentials'
      }
    }
    
    // Verify password
    const isValidPassword = await verifyPassword(password, user.passwordHash)
    if (!isValidPassword) {
      return {
        success: false,
        error: 'Invalid credentials'
      }
    }
    
    // Generate session
    const token = await generateJWT(user)
    const session: Session = {
      userId: user.id,
      user,
      role: user.role,
      permissions: user.permissions,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    }
    
    return {
      success: true,
      user,
      session
    }
    
  } catch (error) {
    console.error('Authentication error:', error)
    return {
      success: false,
      error: 'Authentication failed'
    }
  }
}

/**
 * Get user by ID (mock implementation)
 */
async function getUserById(id: string): Promise<User | null> {
  // Mock user data - in real implementation, this would query the database
  const mockUsers: User[] = [
    {
      id: 'user_1',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      permissions: ['read', 'write', 'delete', 'admin'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'user_2',
      email: '<EMAIL>',
      name: 'Regular User',
      role: 'user',
      permissions: ['read'],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
  
  return mockUsers.find(user => user.id === id) || null
}

/**
 * Get user by email (mock implementation)
 */
async function getUserByEmail(email: string): Promise<(User & { passwordHash: string }) | null> {
  // Mock user data with password hashes
  const mockUsers = [
    {
      id: 'user_1',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      permissions: ['read', 'write', 'delete', 'admin'],
      passwordHash: await hashPassword('admin123'), // In real app, this would be pre-hashed
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'user_2',
      email: '<EMAIL>',
      name: 'Regular User',
      role: 'user',
      permissions: ['read'],
      passwordHash: await hashPassword('user123'),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
  
  return mockUsers.find(user => user.email === email) || null
}

/**
 * Parse cookies from cookie header
 */
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {}
  
  cookieHeader.split(';').forEach(cookie => {
    const [name, value] = cookie.trim().split('=')
    if (name && value) {
      cookies[name] = decodeURIComponent(value)
    }
  })
  
  return cookies
}

/**
 * Parse expires in string to seconds
 */
function parseExpiresIn(expiresIn: string): number {
  const match = expiresIn.match(/^(\d+)([smhd])$/)
  if (!match) {
    return 7 * 24 * 60 * 60 // Default 7 days
  }
  
  const [, amount, unit] = match
  const num = parseInt(amount, 10)
  
  switch (unit) {
    case 's': return num
    case 'm': return num * 60
    case 'h': return num * 60 * 60
    case 'd': return num * 24 * 60 * 60
    default: return 7 * 24 * 60 * 60
  }
}

/**
 * Create signature for JWT (simplified)
 */
async function createSignature(data: string): Promise<string> {
  const secret = 'kilat_jwt_secret' // In real app, this would be from environment
  const encoder = new TextEncoder()
  const keyData = encoder.encode(secret)
  const messageData = encoder.encode(data)
  
  const key = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  )
  
  const signature = await crypto.subtle.sign('HMAC', key, messageData)
  const signatureArray = Array.from(new Uint8Array(signature))
  return btoa(String.fromCharCode(...signatureArray))
}
