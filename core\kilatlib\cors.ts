/**
 * <PERSON><PERSON>Lib CORS - Cross-Origin Resource Sharing utilities
 * Handles CORS headers and preflight requests
 */

interface CORSOptions {
  origin?: string | string[] | boolean | ((origin: string) => boolean)
  methods?: string | string[]
  allowedHeaders?: string | string[]
  exposedHeaders?: string | string[]
  credentials?: boolean
  maxAge?: number
  preflightContinue?: boolean
  optionsSuccessStatus?: number
}

interface CORSResult {
  headers: Headers
  shouldContinue: boolean
  status?: number
}

/**
 * Apply CORS headers to a request
 */
export function cors(request: Request, options: CORSOptions = {}): CORSResult {
  const headers = new Headers()
  const origin = request.headers.get('origin')
  const method = request.method
  
  // Default options
  const defaultOptions: Required<CORSOptions> = {
    origin: '*',
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: [],
    credentials: false,
    maxAge: 86400, // 24 hours
    preflightContinue: false,
    optionsSuccessStatus: 204
  }
  
  const config = { ...defaultOptions, ...options }
  
  // Handle origin
  const allowedOrigin = getAllowedOrigin(origin, config.origin)
  if (allowedOrigin) {
    headers.set('Access-Control-Allow-Origin', allowedOrigin)
  }
  
  // Handle credentials
  if (config.credentials) {
    headers.set('Access-Control-Allow-Credentials', 'true')
  }
  
  // Handle exposed headers
  if (config.exposedHeaders.length > 0) {
    const exposedHeaders = Array.isArray(config.exposedHeaders) 
      ? config.exposedHeaders.join(', ')
      : config.exposedHeaders
    headers.set('Access-Control-Expose-Headers', exposedHeaders)
  }
  
  // Handle preflight requests
  if (method === 'OPTIONS') {
    return handlePreflight(request, headers, config)
  }
  
  return {
    headers,
    shouldContinue: true
  }
}

/**
 * Handle preflight OPTIONS requests
 */
function handlePreflight(
  request: Request, 
  headers: Headers, 
  config: Required<CORSOptions>
): CORSResult {
  const requestMethod = request.headers.get('access-control-request-method')
  const requestHeaders = request.headers.get('access-control-request-headers')
  
  // Handle methods
  const allowedMethods = Array.isArray(config.methods) 
    ? config.methods.join(', ')
    : config.methods
  headers.set('Access-Control-Allow-Methods', allowedMethods)
  
  // Handle headers
  if (requestHeaders) {
    const allowedHeaders = Array.isArray(config.allowedHeaders)
      ? config.allowedHeaders.join(', ')
      : config.allowedHeaders
    headers.set('Access-Control-Allow-Headers', allowedHeaders)
  }
  
  // Handle max age
  headers.set('Access-Control-Max-Age', config.maxAge.toString())
  
  // Vary header for caching
  headers.set('Vary', 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers')
  
  return {
    headers,
    shouldContinue: !config.preflightContinue,
    status: config.optionsSuccessStatus
  }
}

/**
 * Get allowed origin based on configuration
 */
function getAllowedOrigin(
  requestOrigin: string | null, 
  configOrigin: string | string[] | boolean | ((origin: string) => boolean)
): string | null {
  // No origin in request
  if (!requestOrigin) {
    return typeof configOrigin === 'string' ? configOrigin : null
  }
  
  // Allow all origins
  if (configOrigin === true || configOrigin === '*') {
    return '*'
  }
  
  // Deny all origins
  if (configOrigin === false) {
    return null
  }
  
  // Function to determine allowed origin
  if (typeof configOrigin === 'function') {
    return configOrigin(requestOrigin) ? requestOrigin : null
  }
  
  // String origin
  if (typeof configOrigin === 'string') {
    return configOrigin === requestOrigin ? requestOrigin : null
  }
  
  // Array of origins
  if (Array.isArray(configOrigin)) {
    return configOrigin.includes(requestOrigin) ? requestOrigin : null
  }
  
  return null
}

/**
 * Create CORS middleware for specific routes
 */
export function createCORSMiddleware(options: CORSOptions = {}) {
  return (request: Request): CORSResult => {
    return cors(request, options)
  }
}

/**
 * Predefined CORS configurations
 */
export const corsPresets = {
  /**
   * Allow all origins (development)
   */
  allowAll: {
    origin: true,
    credentials: true,
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  } as CORSOptions,
  
  /**
   * Strict CORS (production)
   */
  strict: {
    origin: false,
    credentials: false,
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type'],
  } as CORSOptions,
  
  /**
   * API CORS (for REST APIs)
   */
  api: {
    origin: true,
    credentials: true,
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'X-Requested-With', 
      'Accept', 
      'Origin',
      'X-API-Key'
    ],
    exposedHeaders: [
      'X-Total-Count',
      'X-Page-Count',
      'X-Rate-Limit-Limit',
      'X-Rate-Limit-Remaining',
      'X-Rate-Limit-Reset'
    ],
  } as CORSOptions,
  
  /**
   * WebSocket CORS
   */
  websocket: {
    origin: true,
    credentials: true,
    methods: ['GET'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Sec-WebSocket-Protocol',
      'Sec-WebSocket-Extensions'
    ],
  } as CORSOptions,
}

/**
 * Check if request is a CORS request
 */
export function isCORSRequest(request: Request): boolean {
  return request.headers.has('origin')
}

/**
 * Check if request is a preflight request
 */
export function isPreflightRequest(request: Request): boolean {
  return (
    request.method === 'OPTIONS' &&
    request.headers.has('origin') &&
    request.headers.has('access-control-request-method')
  )
}

/**
 * Get CORS headers for a response
 */
export function getCORSHeaders(request: Request, options: CORSOptions = {}): Headers {
  const result = cors(request, options)
  return result.headers
}

/**
 * Apply CORS headers to an existing response
 */
export function applyCORSHeaders(
  response: Response, 
  request: Request, 
  options: CORSOptions = {}
): Response {
  const corsHeaders = getCORSHeaders(request, options)
  
  // Clone response to modify headers
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers
  })
  
  // Add CORS headers
  for (const [key, value] of corsHeaders.entries()) {
    newResponse.headers.set(key, value)
  }
  
  return newResponse
}

/**
 * Create a CORS-enabled Response
 */
export function createCORSResponse(
  body: BodyInit | null,
  request: Request,
  init: ResponseInit = {},
  corsOptions: CORSOptions = {}
): Response {
  const corsHeaders = getCORSHeaders(request, corsOptions)
  
  // Merge headers
  const headers = new Headers(init.headers)
  for (const [key, value] of corsHeaders.entries()) {
    headers.set(key, value)
  }
  
  return new Response(body, {
    ...init,
    headers
  })
}
