/**
 * KilatLib Generator - Code generation utilities for CLI
 * Generates pages, components, APIs, services, and other boilerplate code
 */

import { writeFile, mkdir } from 'fs/promises'
import { join, dirname } from 'path'
import { KilatConfig } from '../kilatcore/types'
import { capitalize, kebabCase, camelCase } from './utils'

export class Generator {
  private config: KilatConfig
  
  constructor(config: KilatConfig) {
    this.config = config
  }
  
  /**
   * Generate code based on type and name
   */
  async generate(type: string, name: string, options: string[] = []): Promise<void> {
    switch (type) {
      case 'page':
        await this.generatePage(name, options)
        break
      case 'api':
        await this.generateAPI(name, options)
        break
      case 'component':
        await this.generateComponent(name, options)
        break
      case 'service':
        await this.generateService(name, options)
        break
      case 'layout':
        await this.generateLayout(name, options)
        break
      case 'plugin':
        await this.generatePlugin(name, options)
        break
      default:
        throw new Error(`Unknown generator type: ${type}`)
    }
  }
  
  /**
   * Generate a new page
   */
  private async generatePage(name: string, options: string[]): Promise<void> {
    const pagePath = join(this.config.apps.dir, name, 'page.tsx')
    const componentName = `${capitalize(camelCase(name))}Page`
    
    const content = `/**
 * ${componentName}
 * Route: /${name}
 */

import { KilatCard } from '@/components/ui/card'
import { KilatButton } from '@/components/ui/button'

export default function ${componentName}() {
  return (
    <div className="space-y-8">
      <div className="animate-kilat-fade-in">
        <h1 className="text-3xl font-bold mb-2">${capitalize(name)}</h1>
        <p className="text-glow-muted">Welcome to the ${name} page</p>
      </div>

      <KilatCard className="animate-kilat-slide-up">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Content</h2>
          <p className="text-glow-muted mb-4">
            This is a generated page. You can customize it by editing the file at:
          </p>
          <code className="bg-glow-surface px-2 py-1 rounded text-sm">
            apps/${name}/page.tsx
          </code>
          
          <div className="mt-6">
            <KilatButton variant="primary">
              Get Started
            </KilatButton>
          </div>
        </div>
      </KilatCard>
    </div>
  )
}
`
    
    await this.ensureDir(dirname(pagePath))
    await writeFile(pagePath, content)
    
    console.log(`✅ Generated page: ${pagePath}`)
  }
  
  /**
   * Generate a new API route
   */
  private async generateAPI(name: string, options: string[]): Promise<void> {
    const apiPath = join(this.config.apps.dir, 'api', name, 'route.ts')
    const serviceName = `${capitalize(camelCase(name))}Service`
    
    const content = `/**
 * ${capitalize(name)} API Routes
 * Handles CRUD operations for ${name}
 */

import { NextRequest } from 'next/server'
import { ${serviceName} } from '@/core/kilatservice/${kebabCase(name)}.service'

const ${camelCase(name)}Service = new ${serviceName}()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    
    const result = await ${camelCase(name)}Service.getAll({
      page,
      limit
    })
    
    return Response.json({
      success: true,
      data: result.items,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages: Math.ceil(result.total / limit)
      }
    })
    
  } catch (error) {
    console.error('${capitalize(name)} GET Error:', error)
    return Response.json(
      { error: 'Failed to fetch ${name}' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const result = await ${camelCase(name)}Service.create(body)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      data: result.data
    }, { status: 201 })
    
  } catch (error) {
    console.error('${capitalize(name)} POST Error:', error)
    return Response.json(
      { error: 'Failed to create ${name}' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return Response.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }
    
    const body = await request.json()
    const result = await ${camelCase(name)}Service.update(id, body)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      data: result.data
    })
    
  } catch (error) {
    console.error('${capitalize(name)} PUT Error:', error)
    return Response.json(
      { error: 'Failed to update ${name}' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    
    if (!id) {
      return Response.json(
        { error: 'ID is required' },
        { status: 400 }
      )
    }
    
    const result = await ${camelCase(name)}Service.delete(id)
    
    if (!result.success) {
      return Response.json(
        { error: result.error },
        { status: 400 }
      )
    }
    
    return Response.json({
      success: true,
      message: '${capitalize(name)} deleted successfully'
    })
    
  } catch (error) {
    console.error('${capitalize(name)} DELETE Error:', error)
    return Response.json(
      { error: 'Failed to delete ${name}' },
      { status: 500 }
    )
  }
}
`
    
    await this.ensureDir(dirname(apiPath))
    await writeFile(apiPath, content)
    
    console.log(`✅ Generated API route: ${apiPath}`)
    
    // Also generate the corresponding service
    await this.generateService(name, options)
  }
  
  /**
   * Generate a new component
   */
  private async generateComponent(name: string, options: string[]): Promise<void> {
    const componentPath = join('components', 'ui', `${kebabCase(name)}.tsx`)
    const componentName = capitalize(camelCase(name))
    
    const content = `/**
 * ${componentName} - Reusable component
 */

import { ReactNode } from 'react'
import { cn } from '@/core/kilatlib/utils'

interface ${componentName}Props {
  children?: ReactNode
  className?: string
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
}

export function ${componentName}({ 
  children, 
  className,
  variant = 'default',
  size = 'md',
  ...props 
}: ${componentName}Props) {
  return (
    <div
      className={cn(
        'kilat-${kebabCase(name)}',
        // Base styles
        'rounded-lg transition-all duration-200',
        // Variant styles
        {
          'bg-glow-surface border border-glow-surface': variant === 'default',
          'bg-glow-primary text-white': variant === 'primary',
          'bg-glow-secondary text-white': variant === 'secondary',
        },
        // Size styles
        {
          'p-2 text-sm': size === 'sm',
          'p-4': size === 'md',
          'p-6 text-lg': size === 'lg',
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}
`
    
    await this.ensureDir(dirname(componentPath))
    await writeFile(componentPath, content)
    
    console.log(`✅ Generated component: ${componentPath}`)
  }
  
  /**
   * Generate a new service
   */
  private async generateService(name: string, options: string[]): Promise<void> {
    const servicePath = join('core', 'kilatservice', `${kebabCase(name)}.service.ts`)
    const serviceName = `${capitalize(camelCase(name))}Service`
    const modelName = capitalize(camelCase(name))
    
    const content = `/**
 * ${serviceName} - Business logic for ${name}
 * Handles CRUD operations and business rules
 */

import { KilatORM } from '@/core/kilatorm'
import { generateId } from '@/core/kilatlib/utils'

interface ${modelName} {
  id: string
  name: string
  createdAt: Date
  updatedAt: Date
}

interface Create${modelName}Data {
  name: string
}

interface Update${modelName}Data {
  name?: string
}

interface GetAllOptions {
  page: number
  limit: number
  search?: string
}

interface GetAllResult {
  items: ${modelName}[]
  total: number
}

interface ServiceResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

export class ${serviceName} {
  private orm: KilatORM
  
  constructor() {
    this.orm = new KilatORM()
  }
  
  /**
   * Get all ${name} with pagination
   */
  async getAll(options: GetAllOptions): Promise<GetAllResult> {
    try {
      const { page, limit, search } = options
      const offset = (page - 1) * limit
      
      let query: any = {}
      
      if (search) {
        query = {
          name: { $regex: search, $options: 'i' }
        }
      }
      
      const [items, total] = await Promise.all([
        this.orm.${camelCase(name)}.find(query)
          .skip(offset)
          .limit(limit)
          .sort({ createdAt: -1 }),
        this.orm.${camelCase(name)}.countDocuments(query)
      ])
      
      return { items, total }
      
    } catch (error) {
      console.error('Get all ${name} error:', error)
      return { items: [], total: 0 }
    }
  }
  
  /**
   * Get ${name} by ID
   */
  async getById(id: string): Promise<${modelName} | null> {
    try {
      return await this.orm.${camelCase(name)}.findById(id)
    } catch (error) {
      console.error('Get ${name} by ID error:', error)
      return null
    }
  }
  
  /**
   * Create new ${name}
   */
  async create(data: Create${modelName}Data): Promise<ServiceResult> {
    try {
      const ${camelCase(name)} = await this.orm.${camelCase(name)}.create({
        id: generateId('${camelCase(name)}'),
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      return {
        success: true,
        data: ${camelCase(name)}
      }
      
    } catch (error) {
      console.error('Create ${name} error:', error)
      return {
        success: false,
        error: 'Failed to create ${name}'
      }
    }
  }
  
  /**
   * Update ${name}
   */
  async update(id: string, data: Update${modelName}Data): Promise<ServiceResult> {
    try {
      const existing = await this.orm.${camelCase(name)}.findById(id)
      
      if (!existing) {
        return {
          success: false,
          error: '${capitalize(name)} not found'
        }
      }
      
      const updated = await this.orm.${camelCase(name)}.updateOne(
        { id },
        {
          ...data,
          updatedAt: new Date()
        }
      )
      
      return {
        success: true,
        data: updated
      }
      
    } catch (error) {
      console.error('Update ${name} error:', error)
      return {
        success: false,
        error: 'Failed to update ${name}'
      }
    }
  }
  
  /**
   * Delete ${name}
   */
  async delete(id: string): Promise<ServiceResult> {
    try {
      const existing = await this.orm.${camelCase(name)}.findById(id)
      
      if (!existing) {
        return {
          success: false,
          error: '${capitalize(name)} not found'
        }
      }
      
      await this.orm.${camelCase(name)}.deleteOne({ id })
      
      return {
        success: true
      }
      
    } catch (error) {
      console.error('Delete ${name} error:', error)
      return {
        success: false,
        error: 'Failed to delete ${name}'
      }
    }
  }
}
`
    
    await this.ensureDir(dirname(servicePath))
    await writeFile(servicePath, content)
    
    console.log(`✅ Generated service: ${servicePath}`)
  }
  
  /**
   * Generate a new layout
   */
  private async generateLayout(name: string, options: string[]): Promise<void> {
    const layoutPath = join(this.config.apps.dir, name, 'layout.tsx')
    const layoutName = `${capitalize(camelCase(name))}Layout`
    
    const content = `/**
 * ${layoutName}
 * Layout for /${name} routes
 */

export default function ${layoutName}({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="min-h-screen bg-glow-background">
      <div className="kilat-container">
        <header className="py-8">
          <h1 className="text-2xl font-bold text-glow-text">
            ${capitalize(name)}
          </h1>
        </header>
        
        <main className="pb-8">
          {children}
        </main>
        
        <footer className="py-4 border-t border-glow-surface">
          <p className="text-sm text-glow-muted text-center">
            ${capitalize(name)} section powered by Kilat.js
          </p>
        </footer>
      </div>
    </div>
  )
}
`
    
    await this.ensureDir(dirname(layoutPath))
    await writeFile(layoutPath, content)
    
    console.log(`✅ Generated layout: ${layoutPath}`)
  }
  
  /**
   * Generate a new plugin
   */
  private async generatePlugin(name: string, options: string[]): Promise<void> {
    const pluginPath = join(this.config.plugins.dir, `${kebabCase(name)}.ts`)
    const pluginName = `${camelCase(name)}Plugin`
    
    const content = `/**
 * ${capitalize(name)} Plugin for Kilat.js
 * Custom plugin generated by Kilat CLI
 */

import { Plugin, BuildContext } from '@/core/kilatcore/types'

export const ${pluginName}: Plugin = {
  name: '${kebabCase(name)}',
  
  async setup(build: BuildContext) {
    console.log('🔌 ${capitalize(name)} plugin initialized')
    
    // Plugin setup logic goes here
    
    // Example: Handle specific file types
    build.onLoad(/\\.${name}$/, async (args) => {
      // Process .${name} files
      const content = await readFile(args.path, 'utf-8')
      
      // Transform content
      const transformed = transform${capitalize(name)}(content)
      
      return {
        contents: transformed,
        loader: 'js'
      }
    })
    
    // Example: Transform imports
    build.onResolve(/^${name}:/, async (args) => {
      // Handle ${name}: imports
      return {
        path: args.path.replace('${name}:', './'),
        namespace: '${name}'
      }
    })
  }
}

/**
 * Transform ${name} content
 */
function transform${capitalize(name)}(content: string): string {
  // Add your transformation logic here
  return content
}

export default ${pluginName}
`
    
    await this.ensureDir(dirname(pluginPath))
    await writeFile(pluginPath, content)
    
    console.log(`✅ Generated plugin: ${pluginPath}`)
  }
  
  /**
   * Ensure directory exists
   */
  private async ensureDir(dir: string): Promise<void> {
    try {
      await mkdir(dir, { recursive: true })
    } catch (error) {
      // Directory might already exist
    }
  }
}
