/**
 * KilatLib Rate Limit - Rate limiting utilities
 * Implements token bucket and sliding window algorithms
 */

interface RateLimitOptions {
  windowMs: number    // Time window in milliseconds
  max: number         // Maximum requests per window
  message?: string    // Error message
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  keyGenerator?: (request: Request) => string
}

interface RateLimitResult {
  success: boolean
  remaining: number
  resetTime: number
  retryAfter?: number
}

interface RateLimitEntry {
  count: number
  resetTime: number
  requests: number[]
}

// In-memory store for rate limiting
// In production, you'd use Redis or similar
const rateLimitStore = new Map<string, RateLimitEntry>()

/**
 * Apply rate limiting to a request
 */
export async function rateLimit(
  request: Request, 
  options: RateLimitOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // 100 requests per window
  }
): Promise<RateLimitResult> {
  const key = options.keyGenerator ? options.keyGenerator(request) : getClientKey(request)
  const now = Date.now()
  const windowMs = options.windowMs
  const maxRequests = options.max
  
  // Get or create rate limit entry
  let entry = rateLimitStore.get(key)
  if (!entry) {
    entry = {
      count: 0,
      resetTime: now + windowMs,
      requests: []
    }
    rateLimitStore.set(key, entry)
  }
  
  // Clean up old requests (sliding window)
  entry.requests = entry.requests.filter(timestamp => timestamp > now - windowMs)
  
  // Check if limit exceeded
  if (entry.requests.length >= maxRequests) {
    const oldestRequest = Math.min(...entry.requests)
    const retryAfter = Math.ceil((oldestRequest + windowMs - now) / 1000)
    
    return {
      success: false,
      remaining: 0,
      resetTime: oldestRequest + windowMs,
      retryAfter
    }
  }
  
  // Add current request
  entry.requests.push(now)
  entry.count = entry.requests.length
  
  // Update reset time
  if (now >= entry.resetTime) {
    entry.resetTime = now + windowMs
  }
  
  return {
    success: true,
    remaining: maxRequests - entry.count,
    resetTime: entry.resetTime
  }
}

/**
 * Rate limit by IP address
 */
export async function rateLimitByIP(
  request: Request,
  options: Partial<RateLimitOptions> = {}
): Promise<RateLimitResult> {
  const defaultOptions: RateLimitOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100,
    keyGenerator: (req) => `ip:${getClientIP(req)}`
  }
  
  return rateLimit(request, { ...defaultOptions, ...options })
}

/**
 * Rate limit by user ID
 */
export async function rateLimitByUser(
  request: Request,
  userId: string,
  options: Partial<RateLimitOptions> = {}
): Promise<RateLimitResult> {
  const defaultOptions: RateLimitOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // Higher limit for authenticated users
    keyGenerator: () => `user:${userId}`
  }
  
  return rateLimit(request, { ...defaultOptions, ...options })
}

/**
 * Rate limit API endpoints
 */
export async function rateLimitAPI(
  request: Request,
  endpoint: string,
  options: Partial<RateLimitOptions> = {}
): Promise<RateLimitResult> {
  const clientKey = getClientKey(request)
  const defaultOptions: RateLimitOptions = {
    windowMs: 60 * 1000, // 1 minute
    max: 60, // 60 requests per minute
    keyGenerator: () => `api:${endpoint}:${clientKey}`
  }
  
  return rateLimit(request, { ...defaultOptions, ...options })
}

/**
 * Rate limit login attempts
 */
export async function rateLimitLogin(
  request: Request,
  identifier: string, // email or username
  options: Partial<RateLimitOptions> = {}
): Promise<RateLimitResult> {
  const ip = getClientIP(request)
  const defaultOptions: RateLimitOptions = {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 login attempts per 15 minutes
    keyGenerator: () => `login:${identifier}:${ip}`
  }
  
  return rateLimit(request, { ...defaultOptions, ...options })
}

/**
 * Rate limit password reset attempts
 */
export async function rateLimitPasswordReset(
  request: Request,
  email: string,
  options: Partial<RateLimitOptions> = {}
): Promise<RateLimitResult> {
  const defaultOptions: RateLimitOptions = {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 password reset attempts per hour
    keyGenerator: () => `password-reset:${email}`
  }
  
  return rateLimit(request, { ...defaultOptions, ...options })
}

/**
 * Get client key for rate limiting
 */
function getClientKey(request: Request): string {
  const ip = getClientIP(request)
  const userAgent = request.headers.get('user-agent') || 'unknown'
  
  // Create a simple hash of IP + User Agent for better uniqueness
  return `${ip}:${hashString(userAgent)}`
}

/**
 * Get client IP address
 */
function getClientIP(request: Request): string {
  // Try various headers for client IP
  const headers = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip',
    'x-cluster-client-ip',
    'x-forwarded',
    'forwarded-for',
    'forwarded'
  ]
  
  for (const header of headers) {
    const value = request.headers.get(header)
    if (value) {
      // Take the first IP if there are multiple
      const ip = value.split(',')[0].trim()
      if (isValidIP(ip)) {
        return ip
      }
    }
  }
  
  // Fallback to a default IP
  return '127.0.0.1'
}

/**
 * Check if string is a valid IP address
 */
function isValidIP(ip: string): boolean {
  // Simple IPv4 validation
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  if (ipv4Regex.test(ip)) {
    const parts = ip.split('.')
    return parts.every(part => {
      const num = parseInt(part, 10)
      return num >= 0 && num <= 255
    })
  }
  
  // Simple IPv6 validation
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
  return ipv6Regex.test(ip)
}

/**
 * Simple string hash function
 */
function hashString(str: string): string {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36)
}

/**
 * Clean up expired rate limit entries
 */
export function cleanupRateLimitStore(): void {
  const now = Date.now()
  
  for (const [key, entry] of rateLimitStore.entries()) {
    // Remove entries that are completely expired
    entry.requests = entry.requests.filter(timestamp => timestamp > now - 24 * 60 * 60 * 1000) // Keep last 24 hours
    
    if (entry.requests.length === 0 && entry.resetTime < now) {
      rateLimitStore.delete(key)
    }
  }
}

/**
 * Get rate limit statistics
 */
export function getRateLimitStats(): {
  totalKeys: number
  totalRequests: number
  memoryUsage: number
} {
  let totalRequests = 0
  
  for (const entry of rateLimitStore.values()) {
    totalRequests += entry.requests.length
  }
  
  // Estimate memory usage (rough calculation)
  const memoryUsage = rateLimitStore.size * 100 + totalRequests * 8 // bytes
  
  return {
    totalKeys: rateLimitStore.size,
    totalRequests,
    memoryUsage
  }
}

/**
 * Reset rate limit for a specific key
 */
export function resetRateLimit(key: string): boolean {
  return rateLimitStore.delete(key)
}

/**
 * Get rate limit info for a specific key
 */
export function getRateLimitInfo(key: string): RateLimitEntry | null {
  return rateLimitStore.get(key) || null
}

// Cleanup expired entries every 5 minutes
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupRateLimitStore, 5 * 60 * 1000)
}
