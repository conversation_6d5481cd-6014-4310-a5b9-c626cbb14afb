/**
 * KilatMeta Head - HTML head management for SEO and metadata
 * Handles title, meta tags, Open Graph, Twitter Cards, and JSON-LD
 */

import { KilatConfig } from '../kilatcore/types'

interface MetaTag {
  name?: string
  property?: string
  content: string
  httpEquiv?: string
}

interface LinkTag {
  rel: string
  href: string
  type?: string
  sizes?: string
  media?: string
}

interface OpenGraphData {
  title?: string
  description?: string
  image?: string
  url?: string
  type?: string
  siteName?: string
  locale?: string
}

interface TwitterCardData {
  card?: 'summary' | 'summary_large_image' | 'app' | 'player'
  site?: string
  creator?: string
  title?: string
  description?: string
  image?: string
}

interface JSONLDData {
  '@context': string
  '@type': string
  [key: string]: any
}

export class KilatHead {
  private config: KilatConfig
  private metaTags: MetaTag[] = []
  private linkTags: LinkTag[] = []
  private title: string = ''
  private jsonLD: JSONLDData[] = []
  
  constructor(config: KilatConfig) {
    this.config = config
    this.title = config.meta.defaultTitle
    
    // Add default meta tags
    this.addDefaultMetaTags()
  }
  
  /**
   * Set page title
   */
  setTitle(title: string): void {
    if (this.config.meta.titleTemplate) {
      this.title = this.config.meta.titleTemplate.replace('%s', title)
    } else {
      this.title = title
    }
  }
  
  /**
   * Add meta tag
   */
  addMeta(meta: MetaTag): void {
    // Remove existing meta tag with same name/property
    this.metaTags = this.metaTags.filter(tag => 
      tag.name !== meta.name && tag.property !== meta.property
    )
    
    this.metaTags.push(meta)
  }
  
  /**
   * Add link tag
   */
  addLink(link: LinkTag): void {
    // Remove existing link with same rel and href
    this.linkTags = this.linkTags.filter(tag => 
      !(tag.rel === link.rel && tag.href === link.href)
    )
    
    this.linkTags.push(link)
  }
  
  /**
   * Set Open Graph data
   */
  setOpenGraph(og: OpenGraphData): void {
    if (og.title) {
      this.addMeta({ property: 'og:title', content: og.title })
    }
    
    if (og.description) {
      this.addMeta({ property: 'og:description', content: og.description })
    }
    
    if (og.image) {
      this.addMeta({ property: 'og:image', content: og.image })
    }
    
    if (og.url) {
      this.addMeta({ property: 'og:url', content: og.url })
    }
    
    if (og.type) {
      this.addMeta({ property: 'og:type', content: og.type })
    }
    
    if (og.siteName) {
      this.addMeta({ property: 'og:site_name', content: og.siteName })
    }
    
    if (og.locale) {
      this.addMeta({ property: 'og:locale', content: og.locale })
    }
  }
  
  /**
   * Set Twitter Card data
   */
  setTwitterCard(twitter: TwitterCardData): void {
    if (twitter.card) {
      this.addMeta({ name: 'twitter:card', content: twitter.card })
    }
    
    if (twitter.site) {
      this.addMeta({ name: 'twitter:site', content: twitter.site })
    }
    
    if (twitter.creator) {
      this.addMeta({ name: 'twitter:creator', content: twitter.creator })
    }
    
    if (twitter.title) {
      this.addMeta({ name: 'twitter:title', content: twitter.title })
    }
    
    if (twitter.description) {
      this.addMeta({ name: 'twitter:description', content: twitter.description })
    }
    
    if (twitter.image) {
      this.addMeta({ name: 'twitter:image', content: twitter.image })
    }
  }
  
  /**
   * Add JSON-LD structured data
   */
  addJSONLD(data: JSONLDData): void {
    this.jsonLD.push(data)
  }
  
  /**
   * Set canonical URL
   */
  setCanonical(url: string): void {
    this.addLink({ rel: 'canonical', href: url })
  }
  
  /**
   * Add favicon
   */
  setFavicon(href: string, type: string = 'image/x-icon'): void {
    this.addLink({ rel: 'icon', href, type })
  }
  
  /**
   * Add Apple touch icon
   */
  setAppleTouchIcon(href: string, sizes?: string): void {
    this.addLink({ rel: 'apple-touch-icon', href, sizes })
  }
  
  /**
   * Add manifest for PWA
   */
  setManifest(href: string): void {
    this.addLink({ rel: 'manifest', href })
  }
  
  /**
   * Add preload link
   */
  addPreload(href: string, as: string, type?: string): void {
    this.addLink({ rel: 'preload', href, type: `as=${as}${type ? `;type=${type}` : ''}` })
  }
  
  /**
   * Add prefetch link
   */
  addPrefetch(href: string): void {
    this.addLink({ rel: 'prefetch', href })
  }
  
  /**
   * Add DNS prefetch
   */
  addDNSPrefetch(href: string): void {
    this.addLink({ rel: 'dns-prefetch', href })
  }
  
  /**
   * Generate complete HTML head content
   */
  generateHead(): string {
    const parts: string[] = []
    
    // Title
    parts.push(`<title>${this.escapeHTML(this.title)}</title>`)
    
    // Meta tags
    for (const meta of this.metaTags) {
      const attributes: string[] = []
      
      if (meta.name) {
        attributes.push(`name="${this.escapeHTML(meta.name)}"`)
      }
      
      if (meta.property) {
        attributes.push(`property="${this.escapeHTML(meta.property)}"`)
      }
      
      if (meta.httpEquiv) {
        attributes.push(`http-equiv="${this.escapeHTML(meta.httpEquiv)}"`)
      }
      
      attributes.push(`content="${this.escapeHTML(meta.content)}"`)
      
      parts.push(`<meta ${attributes.join(' ')}>`)
    }
    
    // Link tags
    for (const link of this.linkTags) {
      const attributes: string[] = []
      
      attributes.push(`rel="${this.escapeHTML(link.rel)}"`)
      attributes.push(`href="${this.escapeHTML(link.href)}"`)
      
      if (link.type) {
        attributes.push(`type="${this.escapeHTML(link.type)}"`)
      }
      
      if (link.sizes) {
        attributes.push(`sizes="${this.escapeHTML(link.sizes)}"`)
      }
      
      if (link.media) {
        attributes.push(`media="${this.escapeHTML(link.media)}"`)
      }
      
      parts.push(`<link ${attributes.join(' ')}>`)
    }
    
    // JSON-LD structured data
    for (const data of this.jsonLD) {
      parts.push(`<script type="application/ld+json">${JSON.stringify(data)}</script>`)
    }
    
    return parts.join('\n')
  }
  
  /**
   * Add default meta tags
   */
  private addDefaultMetaTags(): void {
    // Basic meta tags
    this.addMeta({ name: 'description', content: this.config.meta.description })
    this.addMeta({ name: 'viewport', content: 'width=device-width, initial-scale=1' })
    this.addMeta({ httpEquiv: 'Content-Type', content: 'text/html; charset=utf-8' })
    
    // Default Open Graph
    this.setOpenGraph({
      title: this.config.meta.defaultTitle,
      description: this.config.meta.description,
      type: this.config.meta.openGraph.type,
      locale: this.config.meta.openGraph.locale,
      siteName: this.config.meta.siteName
    })
    
    // Default Twitter Card
    this.setTwitterCard({
      card: 'summary_large_image',
      title: this.config.meta.defaultTitle,
      description: this.config.meta.description
    })
    
    // Default JSON-LD for website
    this.addJSONLD({
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: this.config.meta.siteName,
      description: this.config.meta.description,
      url: typeof window !== 'undefined' ? window.location.origin : ''
    })
  }
  
  /**
   * Escape HTML entities
   */
  private escapeHTML(text: string): string {
    const div = typeof document !== 'undefined' ? document.createElement('div') : null
    if (div) {
      div.textContent = text
      return div.innerHTML
    }
    
    // Fallback for server-side
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;')
  }
  
  /**
   * Reset all meta data
   */
  reset(): void {
    this.metaTags = []
    this.linkTags = []
    this.jsonLD = []
    this.title = this.config.meta.defaultTitle
    this.addDefaultMetaTags()
  }
  
  /**
   * Get current meta data as object
   */
  getMetaData(): {
    title: string
    metaTags: MetaTag[]
    linkTags: LinkTag[]
    jsonLD: JSONLDData[]
  } {
    return {
      title: this.title,
      metaTags: [...this.metaTags],
      linkTags: [...this.linkTags],
      jsonLD: [...this.jsonLD]
    }
  }
}

/**
 * Create KilatHead instance
 */
export function createHead(config: KilatConfig): KilatHead {
  return new KilatHead(config)
}

/**
 * React component for managing head content
 */
export function KilatMeta({ children }: { children?: React.ReactNode }) {
  // This would integrate with React Helmet or similar
  // For now, it's a placeholder component
  
  React.useEffect(() => {
    // Update document head when component mounts/updates
    console.log('🔍 KilatMeta: Updating document head')
  }, [children])
  
  return null
}
