/**
 * KilatORM - Lightweight ORM for Kilat.js
 * Simple and fast database abstraction layer
 */

import { DatabaseConnection } from '../kilatcore/types'

interface QueryOptions {
  limit?: number
  skip?: number
  sort?: Record<string, 1 | -1>
  select?: string[]
}

interface ModelDefinition {
  tableName: string
  schema: Record<string, any>
  timestamps?: boolean
}

export class KilatORM {
  private connection: DatabaseConnection | null = null
  private models: Map<string, Model> = new Map()
  
  constructor() {
    // Initialize with mock connection for now
    this.connection = new MockDatabaseConnection()
  }
  
  /**
   * Connect to database
   */
  async connect(connectionString?: string): Promise<void> {
    if (this.connection) {
      await this.connection.connect()
      console.log('📊 KilatORM: Connected to database')
    }
  }
  
  /**
   * Disconnect from database
   */
  async disconnect(): Promise<void> {
    if (this.connection) {
      await this.connection.disconnect()
      console.log('📊 KilatORM: Disconnected from database')
    }
  }
  
  /**
   * Define a model
   */
  model(name: string, definition: ModelDefinition): Model {
    const model = new Model(name, definition, this.connection!)
    this.models.set(name, model)
    return model
  }
  
  /**
   * Get a model
   */
  getModel(name: string): Model | undefined {
    return this.models.get(name)
  }
  
  /**
   * Get users model (convenience method)
   */
  get users(): Model {
    if (!this.models.has('users')) {
      this.model('users', {
        tableName: 'users',
        schema: {
          id: { type: 'string', primary: true },
          email: { type: 'string', unique: true },
          name: { type: 'string' },
          role: { type: 'string', default: 'user' },
          passwordHash: { type: 'string' },
          createdAt: { type: 'date' },
          updatedAt: { type: 'date' },
        },
        timestamps: true,
      })
    }
    return this.models.get('users')!
  }
}

export class Model {
  private name: string
  private definition: ModelDefinition
  private connection: DatabaseConnection
  
  constructor(name: string, definition: ModelDefinition, connection: DatabaseConnection) {
    this.name = name
    this.definition = definition
    this.connection = connection
  }
  
  /**
   * Find multiple documents
   */
  async find(query: Record<string, any> = {}, options: QueryOptions = {}): Promise<any[]> {
    const sql = this.buildSelectQuery(query, options)
    const result = await this.connection.query(sql)
    return result.rows || []
  }
  
  /**
   * Find one document
   */
  async findOne(query: Record<string, any>): Promise<any | null> {
    const results = await this.find(query, { limit: 1 })
    return results[0] || null
  }
  
  /**
   * Find by ID
   */
  async findById(id: string): Promise<any | null> {
    return this.findOne({ id })
  }
  
  /**
   * Create a new document
   */
  async create(data: Record<string, any>): Promise<any> {
    if (this.definition.timestamps) {
      data.createdAt = new Date()
      data.updatedAt = new Date()
    }
    
    const sql = this.buildInsertQuery(data)
    await this.connection.query(sql)
    return data
  }
  
  /**
   * Update documents
   */
  async updateOne(query: Record<string, any>, update: Record<string, any>): Promise<any> {
    if (this.definition.timestamps) {
      update.updatedAt = new Date()
    }
    
    const sql = this.buildUpdateQuery(query, update)
    await this.connection.query(sql)
    
    // Return updated document
    return this.findOne(query)
  }
  
  /**
   * Update multiple documents
   */
  async updateMany(query: Record<string, any>, update: Record<string, any>): Promise<number> {
    if (this.definition.timestamps) {
      update.updatedAt = new Date()
    }
    
    const sql = this.buildUpdateQuery(query, update)
    const result = await this.connection.query(sql)
    return result.affectedRows || 0
  }
  
  /**
   * Delete one document
   */
  async deleteOne(query: Record<string, any>): Promise<boolean> {
    const sql = this.buildDeleteQuery(query, true)
    const result = await this.connection.query(sql)
    return (result.affectedRows || 0) > 0
  }
  
  /**
   * Delete multiple documents
   */
  async deleteMany(query: Record<string, any>): Promise<number> {
    const sql = this.buildDeleteQuery(query, false)
    const result = await this.connection.query(sql)
    return result.affectedRows || 0
  }
  
  /**
   * Count documents
   */
  async countDocuments(query: Record<string, any> = {}): Promise<number> {
    const sql = this.buildCountQuery(query)
    const result = await this.connection.query(sql)
    return result.rows?.[0]?.count || 0
  }
  
  /**
   * Aggregate data
   */
  async aggregate(pipeline: any[]): Promise<any[]> {
    // Simple aggregation implementation
    // In a real implementation, this would handle complex aggregation pipelines
    return []
  }
  
  /**
   * Build SELECT query
   */
  private buildSelectQuery(query: Record<string, any>, options: QueryOptions): string {
    const tableName = this.definition.tableName
    const whereClause = this.buildWhereClause(query)
    const selectClause = options.select ? options.select.join(', ') : '*'
    
    let sql = `SELECT ${selectClause} FROM ${tableName}`
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`
    }
    
    if (options.sort) {
      const orderBy = Object.entries(options.sort)
        .map(([field, direction]) => `${field} ${direction === 1 ? 'ASC' : 'DESC'}`)
        .join(', ')
      sql += ` ORDER BY ${orderBy}`
    }
    
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`
    }
    
    if (options.skip) {
      sql += ` OFFSET ${options.skip}`
    }
    
    return sql
  }
  
  /**
   * Build INSERT query
   */
  private buildInsertQuery(data: Record<string, any>): string {
    const tableName = this.definition.tableName
    const fields = Object.keys(data).join(', ')
    const values = Object.values(data).map(v => `'${v}'`).join(', ')
    
    return `INSERT INTO ${tableName} (${fields}) VALUES (${values})`
  }
  
  /**
   * Build UPDATE query
   */
  private buildUpdateQuery(query: Record<string, any>, update: Record<string, any>): string {
    const tableName = this.definition.tableName
    const setClause = Object.entries(update)
      .map(([key, value]) => `${key} = '${value}'`)
      .join(', ')
    const whereClause = this.buildWhereClause(query)
    
    let sql = `UPDATE ${tableName} SET ${setClause}`
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`
    }
    
    return sql
  }
  
  /**
   * Build DELETE query
   */
  private buildDeleteQuery(query: Record<string, any>, limitOne: boolean): string {
    const tableName = this.definition.tableName
    const whereClause = this.buildWhereClause(query)
    
    let sql = `DELETE FROM ${tableName}`
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`
    }
    
    if (limitOne) {
      sql += ` LIMIT 1`
    }
    
    return sql
  }
  
  /**
   * Build COUNT query
   */
  private buildCountQuery(query: Record<string, any>): string {
    const tableName = this.definition.tableName
    const whereClause = this.buildWhereClause(query)
    
    let sql = `SELECT COUNT(*) as count FROM ${tableName}`
    
    if (whereClause) {
      sql += ` WHERE ${whereClause}`
    }
    
    return sql
  }
  
  /**
   * Build WHERE clause
   */
  private buildWhereClause(query: Record<string, any>): string {
    if (Object.keys(query).length === 0) {
      return ''
    }
    
    const conditions = Object.entries(query).map(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        // Handle operators like $regex, $gt, $lt, etc.
        if (value.$regex) {
          return `${key} LIKE '%${value.$regex}%'`
        }
        if (value.$gte) {
          return `${key} >= '${value.$gte}'`
        }
        if (value.$lte) {
          return `${key} <= '${value.$lte}'`
        }
        // Add more operators as needed
      }
      
      return `${key} = '${value}'`
    })
    
    return conditions.join(' AND ')
  }
}

/**
 * Mock database connection for development
 */
class MockDatabaseConnection implements DatabaseConnection {
  private data: Map<string, any[]> = new Map()
  
  async connect(): Promise<void> {
    // Mock connection
    this.data.set('users', [
      {
        id: 'user_1',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin',
        passwordHash: 'hashed_password',
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    ])
  }
  
  async disconnect(): Promise<void> {
    // Mock disconnection
  }
  
  async query(sql: string, params?: any[]): Promise<any> {
    // Mock query execution
    console.log('Mock SQL Query:', sql)
    
    // Simple mock responses
    if (sql.includes('SELECT COUNT(*)')) {
      return { rows: [{ count: 1 }] }
    }
    
    if (sql.includes('SELECT')) {
      return { rows: this.data.get('users') || [] }
    }
    
    if (sql.includes('INSERT') || sql.includes('UPDATE') || sql.includes('DELETE')) {
      return { affectedRows: 1 }
    }
    
    return { rows: [] }
  }
  
  async transaction(callback: (tx: any) => Promise<any>): Promise<any> {
    // Mock transaction
    return callback(this)
  }
}
