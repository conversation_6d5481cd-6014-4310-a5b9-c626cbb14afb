/**
 * KilatPack - Internal build engine for Kilat.js
 * Standalone bundler without external dependencies
 */

import { readdir, readFile, writeFile, mkdir, stat } from 'fs/promises'
import { join, extname, dirname, basename } from 'path'
import { KilatConfig, BuildResult } from '../kilatcore/types'
import { <PERSON><PERSON><PERSON>undler } from './bundler'
import { KilatOptimizer } from './optimizer'

export class KilatPack {
  private config: KilatConfig
  private startTime: number = 0
  private bundler: KilatBundler
  private optimizer: KilatOptimizer

  constructor(config: KilatConfig) {
    this.config = config
    this.bundler = new KilatBundler(config)
    this.optimizer = new KilatOptimizer(config)
  }
  
  /**
   * Build the application for production
   */
  async build(): Promise<BuildResult> {
    this.startTime = Date.now()
    console.log('📦 KilatPack: Starting build process...')
    
    const result: BuildResult = {
      success: false,
      errors: [],
      warnings: [],
      assets: [],
      duration: 0,
      size: 0
    }
    
    try {
      // Ensure output directory exists
      await this.ensureOutputDir()
      
      // Build steps
      await this.buildPages()
      await this.buildAPI()
      await this.buildAssets()
      await this.buildCSS()

      // Bundle JavaScript modules
      await this.bundleModules()

      // Optimize assets
      await this.optimizeAssets()
      
      result.success = true
      result.duration = Date.now() - this.startTime
      
      console.log('✅ KilatPack: Build completed successfully!')
      
    } catch (error) {
      result.errors.push(error instanceof Error ? error.message : String(error))
      console.error('❌ KilatPack: Build failed:', error)
    }
    
    return result
  }
  
  /**
   * Export to static files (SSG)
   */
  async exportStatic(): Promise<void> {
    console.log('📤 KilatPack: Exporting static files...')
    
    try {
      // Ensure export directory exists
      await this.ensureDir(this.config.export.outDir)
      
      // Generate static pages
      await this.generateStaticPages()
      
      // Copy assets
      await this.copyAssets()
      
      // Generate sitemap and robots.txt
      if (this.config.export.generateSitemap) {
        await this.generateSitemap()
      }
      
      if (this.config.export.generateRobots) {
        await this.generateRobots()
      }
      
      console.log('✅ KilatPack: Static export completed!')
      
    } catch (error) {
      console.error('❌ KilatPack: Export failed:', error)
      throw error
    }
  }
  
  /**
   * Ensure output directory exists
   */
  private async ensureOutputDir(): Promise<void> {
    await this.ensureDir(this.config.build.outDir)
  }
  
  /**
   * Ensure directory exists
   */
  private async ensureDir(dir: string): Promise<void> {
    try {
      await stat(dir)
    } catch {
      await mkdir(dir, { recursive: true })
    }
  }
  
  /**
   * Build pages
   */
  private async buildPages(): Promise<void> {
    console.log('🔨 Building pages...')
    
    const pagesDir = this.config.apps.dir
    await this.processPages(pagesDir)
  }
  
  /**
   * Process pages recursively
   */
  private async processPages(dir: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name)
        
        if (entry.isDirectory() && entry.name !== 'api') {
          await this.processPages(fullPath)
        } else if (entry.isFile()) {
          const ext = extname(entry.name)
          const name = basename(entry.name, ext)
          
          if (name === 'page' && this.config.apps.extensions.includes(ext)) {
            await this.buildPage(fullPath)
          }
        }
      }
    } catch (error) {
      console.error(`Error processing pages in ${dir}:`, error)
    }
  }
  
  /**
   * Build individual page
   */
  private async buildPage(pagePath: string): Promise<void> {
    try {
      const content = await readFile(pagePath, 'utf-8')
      
      // Simple transformation (in real implementation, this would be more complex)
      const transformed = await this.transformPage(content, pagePath)
      
      // Write to output directory
      const relativePath = pagePath.replace(this.config.apps.dir, '')
      const outputPath = join(this.config.build.outDir, relativePath)
      
      await this.ensureDir(dirname(outputPath))
      await writeFile(outputPath, transformed)
      
    } catch (error) {
      console.error(`Error building page ${pagePath}:`, error)
    }
  }
  
  /**
   * Transform page content
   */
  private async transformPage(content: string, pagePath: string): Promise<string> {
    console.log(`🔄 Transforming page: ${pagePath}`)

    let transformed = content

    // Transform imports to use absolute paths
    transformed = this.transformImports(transformed)

    // Transform JSX/TSX syntax
    transformed = this.transformJSX(transformed)

    // Optimize code if minification is enabled
    if (this.config.build.minify) {
      transformed = this.minifyCode(transformed)
    }

    // Add source maps if enabled
    if (this.config.build.sourcemap) {
      transformed = this.addSourceMap(transformed, pagePath)
    }

    return transformed
  }

  /**
   * Transform import statements
   */
  private transformImports(content: string): string {
    // Transform @/ imports to relative paths
    return content.replace(
      /import\s+.*?\s+from\s+['"]@\/(.*?)['"]/g,
      (match, path) => {
        return match.replace('@/', '../')
      }
    )
  }

  /**
   * Transform JSX/TSX syntax (simplified)
   */
  private transformJSX(content: string): string {
    // This is a very simplified JSX transformation
    // In a real implementation, you'd use a proper parser like Babel or SWC

    // Transform JSX elements to React.createElement calls
    content = content.replace(
      /<(\w+)([^>]*?)>(.*?)<\/\1>/gs,
      (match, tag, props, children) => {
        const propsObj = this.parseJSXProps(props)
        return `React.createElement('${tag}', ${propsObj}, ${children})`
      }
    )

    return content
  }

  /**
   * Parse JSX props (simplified)
   */
  private parseJSXProps(propsString: string): string {
    if (!propsString.trim()) return 'null'

    // Very basic prop parsing - in reality you'd need a proper parser
    const props = propsString.trim()
    if (props.startsWith('{') && props.endsWith('}')) {
      return props
    }

    return 'null'
  }

  /**
   * Minify code
   */
  private minifyCode(content: string): string {
    // Simple minification - remove comments and extra whitespace
    return content
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
      .replace(/\/\/.*$/gm, '') // Remove line comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .trim()
  }

  /**
   * Add source map
   */
  private addSourceMap(content: string, sourcePath: string): string {
    // Add source map comment
    const sourceMapComment = `\n//# sourceMappingURL=${sourcePath}.map`
    return content + sourceMapComment
  }
  
  /**
   * Build API routes
   */
  private async buildAPI(): Promise<void> {
    console.log('🔨 Building API routes...')
    
    const apiDir = join(this.config.apps.dir, 'api')
    await this.processAPI(apiDir)
  }
  
  /**
   * Process API routes
   */
  private async processAPI(dir: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name)
        
        if (entry.isDirectory()) {
          await this.processAPI(fullPath)
        } else if (entry.isFile()) {
          const ext = extname(entry.name)
          const name = basename(entry.name, ext)
          
          if (name === 'route' && this.config.apps.extensions.includes(ext)) {
            await this.buildAPIRoute(fullPath)
          }
        }
      }
    } catch (error) {
      console.error(`Error processing API in ${dir}:`, error)
    }
  }
  
  /**
   * Build individual API route
   */
  private async buildAPIRoute(routePath: string): Promise<void> {
    try {
      const content = await readFile(routePath, 'utf-8')
      
      // Transform API route
      const transformed = await this.transformAPI(content, routePath)
      
      // Write to output directory
      const relativePath = routePath.replace(this.config.apps.dir, '')
      const outputPath = join(this.config.build.outDir, relativePath)
      
      await this.ensureDir(dirname(outputPath))
      await writeFile(outputPath, transformed)
      
    } catch (error) {
      console.error(`Error building API route ${routePath}:`, error)
    }
  }
  
  /**
   * Transform API content
   */
  private async transformAPI(content: string, routePath: string): Promise<string> {
    // Placeholder transformation
    return content
  }
  
  /**
   * Build assets (CSS, images, etc.)
   */
  private async buildAssets(): Promise<void> {
    console.log('🔨 Building assets...')
    
    // Copy public assets
    await this.copyPublicAssets()
  }
  
  /**
   * Copy public assets
   */
  private async copyPublicAssets(): Promise<void> {
    const publicDir = 'public'
    const outputPublicDir = join(this.config.build.outDir, 'public')
    
    try {
      await this.copyDirectory(publicDir, outputPublicDir)
    } catch (error) {
      console.error('Error copying public assets:', error)
    }
  }
  
  /**
   * Copy directory recursively
   */
  private async copyDirectory(src: string, dest: string): Promise<void> {
    try {
      await this.ensureDir(dest)
      const entries = await readdir(src, { withFileTypes: true })
      
      for (const entry of entries) {
        const srcPath = join(src, entry.name)
        const destPath = join(dest, entry.name)
        
        if (entry.isDirectory()) {
          await this.copyDirectory(srcPath, destPath)
        } else {
          const content = await readFile(srcPath)
          await writeFile(destPath, content)
        }
      }
    } catch (error) {
      // Directory might not exist, which is fine
    }
  }
  
  /**
   * Build CSS
   */
  private async buildCSS(): Promise<void> {
    console.log('🔨 Building CSS...')

    // Process Tailwind CSS and custom styles
    // This would integrate with the KilatCSS system

    try {
      const cssFiles = await this.findCSSFiles()
      for (const cssFile of cssFiles) {
        await this.processCSSFile(cssFile)
      }
    } catch (error) {
      console.error('Error building CSS:', error)
    }
  }

  /**
   * Bundle JavaScript modules
   */
  private async bundleModules(): Promise<void> {
    console.log('📦 Bundling JavaScript modules...')

    try {
      // Find entry points
      const entryPoints = await this.findEntryPoints()

      if (entryPoints.length > 0) {
        const bundleResult = await this.bundler.bundle(entryPoints)

        // Write bundle chunks to output directory
        for (const [chunkName, chunkContent] of bundleResult.chunks) {
          const chunkPath = join(this.config.build.outDir, `${chunkName}.js`)
          await writeFile(chunkPath, chunkContent)
          console.log(`📦 Generated chunk: ${chunkName}.js`)
        }
      }
    } catch (error) {
      console.error('Error bundling modules:', error)
    }
  }

  /**
   * Optimize assets
   */
  private async optimizeAssets(): Promise<void> {
    console.log('🚀 Optimizing assets...')

    try {
      const inputDir = this.config.build.outDir
      const optimizationResult = await this.optimizer.optimizeAssets(inputDir, inputDir)

      // Generate asset manifest
      await this.optimizer.generateAssetManifest(inputDir)

      console.log(`💾 Asset optimization saved ${this.formatBytes(optimizationResult.savings)}`)
    } catch (error) {
      console.error('Error optimizing assets:', error)
    }
  }

  /**
   * Find CSS files to process
   */
  private async findCSSFiles(): Promise<string[]> {
    const cssFiles: string[] = []

    // Look for global CSS files
    const globalCSS = join('core/kilatcss/globals.css')
    try {
      await stat(globalCSS)
      cssFiles.push(globalCSS)
    } catch {
      // File doesn't exist
    }

    return cssFiles
  }

  /**
   * Process individual CSS file
   */
  private async processCSSFile(cssFile: string): Promise<void> {
    try {
      const content = await readFile(cssFile, 'utf-8')
      const outputPath = join(this.config.build.outDir, 'styles.css')

      // Process CSS (in real implementation, this would handle Tailwind, PostCSS, etc.)
      let processedContent = content

      // Minify if enabled
      if (this.config.build.minify) {
        processedContent = this.minifyCSS(processedContent)
      }

      await this.ensureDir(dirname(outputPath))
      await writeFile(outputPath, processedContent)

      console.log(`🎨 Processed CSS: ${basename(cssFile)}`)
    } catch (error) {
      console.error(`Error processing CSS file ${cssFile}:`, error)
    }
  }

  /**
   * Find JavaScript entry points
   */
  private async findEntryPoints(): Promise<string[]> {
    const entryPoints: string[] = []

    // Look for page components
    const pagesDir = this.config.apps.dir
    await this.findEntryPointsRecursive(pagesDir, entryPoints)

    return entryPoints
  }

  /**
   * Recursively find entry points
   */
  private async findEntryPointsRecursive(dir: string, entryPoints: string[]): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })

      for (const entry of entries) {
        const fullPath = join(dir, entry.name)

        if (entry.isDirectory() && entry.name !== 'api') {
          await this.findEntryPointsRecursive(fullPath, entryPoints)
        } else if (entry.isFile()) {
          const ext = extname(entry.name)
          const name = basename(entry.name, ext)

          if (name === 'page' && ['.tsx', '.ts', '.jsx', '.js'].includes(ext)) {
            entryPoints.push(fullPath)
          }
        }
      }
    } catch (error) {
      console.error(`Error finding entry points in ${dir}:`, error)
    }
  }

  /**
   * Minify CSS
   */
  private minifyCSS(content: string): string {
    return content
      .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
      .replace(/\s+/g, ' ') // Collapse whitespace
      .replace(/\s*([{}:;,>+~])\s*/g, '$1') // Remove whitespace around certain characters
      .replace(/;}/g, '}') // Remove trailing semicolons
      .trim()
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  /**
   * Generate static pages for export
   */
  private async generateStaticPages(): Promise<void> {
    // Generate HTML files for each route
    // This would render React components to HTML
  }
  
  /**
   * Copy assets for export
   */
  private async copyAssets(): Promise<void> {
    const buildDir = this.config.build.outDir
    const exportDir = this.config.export.outDir
    
    await this.copyDirectory(buildDir, exportDir)
  }
  
  /**
   * Generate sitemap.xml
   */
  private async generateSitemap(): Promise<void> {
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://example.com/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <priority>1.0</priority>
  </url>
</urlset>`
    
    const sitemapPath = join(this.config.export.outDir, 'sitemap.xml')
    await writeFile(sitemapPath, sitemap)
  }
  
  /**
   * Generate robots.txt
   */
  private async generateRobots(): Promise<void> {
    const robots = `User-agent: *
Allow: /

Sitemap: https://example.com/sitemap.xml`
    
    const robotsPath = join(this.config.export.outDir, 'robots.txt')
    await writeFile(robotsPath, robots)
  }
}
