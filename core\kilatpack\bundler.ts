/**
 * <PERSON>lat<PERSON>ack Bundler - Advanced bundling system
 * Handles module resolution, dependency bundling, and code splitting
 */

import { readFile, writeFile } from 'fs/promises'
import { join, dirname, extname, resolve } from 'path'
import { KilatConfig } from '../kilatcore/types'

interface BundleEntry {
  path: string
  content: string
  dependencies: string[]
  size: number
}

interface BundleResult {
  entries: Map<string, BundleEntry>
  chunks: Map<string, string>
  totalSize: number
  dependencies: Map<string, string[]>
}

export class KilatBundler {
  private config: KilatConfig
  private moduleCache: Map<string, string> = new Map()
  private dependencyGraph: Map<string, Set<string>> = new Map()
  
  constructor(config: KilatConfig) {
    this.config = config
  }
  
  /**
   * Bundle application files
   */
  async bundle(entryPoints: string[]): Promise<BundleResult> {
    console.log('📦 KilatBundler: Starting bundling process...')
    
    const result: BundleResult = {
      entries: new Map(),
      chunks: new Map(),
      totalSize: 0,
      dependencies: new Map()
    }
    
    // Process each entry point
    for (const entryPoint of entryPoints) {
      await this.processEntry(entryPoint, result)
    }
    
    // Generate chunks if code splitting is enabled
    if (this.config.build.splitting) {
      await this.generateChunks(result)
    }
    
    // Tree shake unused code
    if (this.config.build.treeshaking) {
      await this.treeShake(result)
    }
    
    console.log(`✅ KilatBundler: Bundled ${result.entries.size} entries`)
    console.log(`📊 Total bundle size: ${this.formatBytes(result.totalSize)}`)
    
    return result
  }
  
  /**
   * Process a single entry point
   */
  private async processEntry(entryPath: string, result: BundleResult): Promise<void> {
    try {
      const content = await this.loadModule(entryPath)
      const dependencies = await this.resolveDependencies(entryPath, content)
      
      const entry: BundleEntry = {
        path: entryPath,
        content,
        dependencies,
        size: Buffer.byteLength(content, 'utf8')
      }
      
      result.entries.set(entryPath, entry)
      result.dependencies.set(entryPath, dependencies)
      result.totalSize += entry.size
      
      // Process dependencies recursively
      for (const dep of dependencies) {
        if (!result.entries.has(dep)) {
          await this.processEntry(dep, result)
        }
      }
      
    } catch (error) {
      console.error(`Error processing entry ${entryPath}:`, error)
    }
  }
  
  /**
   * Load module content with caching
   */
  private async loadModule(modulePath: string): Promise<string> {
    if (this.moduleCache.has(modulePath)) {
      return this.moduleCache.get(modulePath)!
    }
    
    try {
      const content = await readFile(modulePath, 'utf-8')
      this.moduleCache.set(modulePath, content)
      return content
    } catch (error) {
      console.error(`Failed to load module ${modulePath}:`, error)
      return ''
    }
  }
  
  /**
   * Resolve module dependencies
   */
  private async resolveDependencies(modulePath: string, content: string): Promise<string[]> {
    const dependencies: string[] = []
    const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g
    const requireRegex = /require\(['"]([^'"]+)['"]\)/g
    
    let match
    
    // Find ES6 imports
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1]
      const resolvedPath = await this.resolveModulePath(importPath, modulePath)
      if (resolvedPath) {
        dependencies.push(resolvedPath)
      }
    }
    
    // Find CommonJS requires
    while ((match = requireRegex.exec(content)) !== null) {
      const requirePath = match[1]
      const resolvedPath = await this.resolveModulePath(requirePath, modulePath)
      if (resolvedPath) {
        dependencies.push(resolvedPath)
      }
    }
    
    return dependencies
  }
  
  /**
   * Resolve module path
   */
  private async resolveModulePath(importPath: string, fromPath: string): Promise<string | null> {
    try {
      // Handle relative imports
      if (importPath.startsWith('./') || importPath.startsWith('../')) {
        const basePath = dirname(fromPath)
        let resolvedPath = resolve(basePath, importPath)
        
        // Try different extensions
        const extensions = ['.ts', '.tsx', '.js', '.jsx']
        for (const ext of extensions) {
          try {
            const pathWithExt = resolvedPath + ext
            await readFile(pathWithExt)
            return pathWithExt
          } catch {
            // Try next extension
          }
        }
        
        // Try index files
        for (const ext of extensions) {
          try {
            const indexPath = join(resolvedPath, `index${ext}`)
            await readFile(indexPath)
            return indexPath
          } catch {
            // Try next extension
          }
        }
      }
      
      // Handle absolute imports (@/ prefix)
      if (importPath.startsWith('@/')) {
        const relativePath = importPath.replace('@/', '')
        return this.resolveModulePath('./' + relativePath, fromPath)
      }
      
      // Handle node_modules (for now, we'll skip these)
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        console.log(`Skipping external module: ${importPath}`)
        return null
      }
      
      return null
    } catch (error) {
      console.error(`Failed to resolve module path ${importPath}:`, error)
      return null
    }
  }
  
  /**
   * Generate code chunks for splitting
   */
  private async generateChunks(result: BundleResult): Promise<void> {
    console.log('🔪 Generating code chunks...')
    
    // Group related modules into chunks
    const chunks = new Map<string, string[]>()
    
    // Create vendor chunk for external dependencies
    const vendorModules: string[] = []
    const appModules: string[] = []
    
    for (const [path] of result.entries) {
      if (path.includes('node_modules')) {
        vendorModules.push(path)
      } else {
        appModules.push(path)
      }
    }
    
    if (vendorModules.length > 0) {
      chunks.set('vendor', vendorModules)
    }
    
    // Create app chunk
    if (appModules.length > 0) {
      chunks.set('app', appModules)
    }
    
    // Generate chunk content
    for (const [chunkName, modules] of chunks) {
      const chunkContent = await this.generateChunkContent(modules, result)
      result.chunks.set(chunkName, chunkContent)
    }
  }
  
  /**
   * Generate content for a chunk
   */
  private async generateChunkContent(modules: string[], result: BundleResult): Promise<string> {
    const chunkParts: string[] = []
    
    // Add module loader
    chunkParts.push(this.getModuleLoader())
    
    // Add each module
    for (const modulePath of modules) {
      const entry = result.entries.get(modulePath)
      if (entry) {
        const moduleId = this.getModuleId(modulePath)
        const wrappedModule = this.wrapModule(moduleId, entry.content)
        chunkParts.push(wrappedModule)
      }
    }
    
    return chunkParts.join('\n')
  }
  
  /**
   * Get module loader code
   */
  private getModuleLoader(): string {
    return `
// Kilat.js Module Loader
(function() {
  const modules = {};
  const cache = {};
  
  function require(id) {
    if (cache[id]) return cache[id].exports;
    
    const module = cache[id] = { exports: {} };
    modules[id].call(module.exports, module, module.exports, require);
    
    return module.exports;
  }
  
  window.__kilat_require__ = require;
  window.__kilat_define__ = function(id, factory) {
    modules[id] = factory;
  };
})();
`
  }
  
  /**
   * Wrap module in loader function
   */
  private wrapModule(moduleId: string, content: string): string {
    return `
__kilat_define__("${moduleId}", function(module, exports, require) {
${content}
});
`
  }
  
  /**
   * Get module ID from path
   */
  private getModuleId(modulePath: string): string {
    return modulePath.replace(/[^a-zA-Z0-9]/g, '_')
  }
  
  /**
   * Tree shake unused code
   */
  private async treeShake(result: BundleResult): Promise<void> {
    console.log('🌳 Tree shaking unused code...')
    
    // This is a simplified tree shaking implementation
    // In a real implementation, you'd analyze the AST to find unused exports
    
    for (const [path, entry] of result.entries) {
      const originalSize = entry.size
      
      // Remove unused imports (very basic implementation)
      let content = entry.content
      content = this.removeUnusedImports(content)
      
      // Update entry
      entry.content = content
      entry.size = Buffer.byteLength(content, 'utf8')
      
      const savedBytes = originalSize - entry.size
      if (savedBytes > 0) {
        console.log(`🌳 Removed ${this.formatBytes(savedBytes)} from ${path}`)
      }
    }
  }
  
  /**
   * Remove unused imports (simplified)
   */
  private removeUnusedImports(content: string): string {
    // This is a very basic implementation
    // In reality, you'd need proper AST analysis
    
    const lines = content.split('\n')
    const usedImports = new Set<string>()
    
    // Find all used identifiers
    const identifierRegex = /\b([a-zA-Z_$][a-zA-Z0-9_$]*)\b/g
    let match
    while ((match = identifierRegex.exec(content)) !== null) {
      usedImports.add(match[1])
    }
    
    // Filter out unused imports
    const filteredLines = lines.filter(line => {
      if (line.trim().startsWith('import')) {
        const importMatch = line.match(/import\s+\{([^}]+)\}/)
        if (importMatch) {
          const imports = importMatch[1].split(',').map(i => i.trim())
          const usedInThisImport = imports.some(imp => usedImports.has(imp))
          return usedInThisImport
        }
      }
      return true
    })
    
    return filteredLines.join('\n')
  }
  
  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}
