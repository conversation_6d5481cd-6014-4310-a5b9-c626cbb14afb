/**
 * CSS Processor Plugin - Built-in plugin for CSS processing
 * Handles Tailwind CSS compilation, PostCSS processing, and CSS optimization
 */

import { readFile, writeFile } from 'fs/promises'
import { join, dirname } from 'path'
import { Plugin, BuildContext } from '../../kilatcore/types'

export const cssProcessorPlugin: Plugin = {
  name: 'css-processor',
  
  async setup(build: BuildContext) {
    console.log('🎨 CSS Processor plugin initialized')
    
    // Handle CSS file processing
    build.onLoad(/\.css$/, async (args) => {
      const content = await readFile(args.path, 'utf-8')
      
      let processedContent = content
      
      // Process Tailwind CSS directives
      if (content.includes('@tailwind')) {
        processedContent = await processTailwindCSS(content, build)
      }
      
      // Process PostCSS if needed
      processedContent = await processPostCSS(processedContent, build)
      
      // Minify in production
      if (build.isProd) {
        processedContent = minifyCSS(processedContent)
      }
      
      return {
        contents: processedContent,
        loader: 'css'
      }
    })
    
    // Handle SCSS/Sass files
    build.onLoad(/\.(scss|sass)$/, async (args) => {
      const content = await readFile(args.path, 'utf-8')
      
      // Compile SCSS to CSS (simplified)
      let compiledCSS = await compileSCSS(content, args.path)
      
      // Process like regular CSS
      if (build.isProd) {
        compiledCSS = minifyCSS(compiledCSS)
      }
      
      return {
        contents: compiledCSS,
        loader: 'css'
      }
    })
    
    // Handle CSS modules
    build.onLoad(/\.module\.css$/, async (args) => {
      const content = await readFile(args.path, 'utf-8')
      
      const { css, classNames } = await processCSSModules(content, args.path)
      
      return {
        contents: `
          const styles = ${JSON.stringify(classNames)};
          export default styles;
        `,
        loader: 'js'
      }
    })
  }
}

/**
 * Process Tailwind CSS directives
 */
async function processTailwindCSS(content: string, build: BuildContext): Promise<string> {
  console.log('🌊 Processing Tailwind CSS...')
  
  // This is a simplified Tailwind processing
  // In a real implementation, you'd use the actual Tailwind CSS compiler
  
  let processed = content
  
  // Replace @tailwind directives with actual CSS
  processed = processed.replace('@tailwind base;', getTailwindBase())
  processed = processed.replace('@tailwind components;', getTailwindComponents())
  processed = processed.replace('@tailwind utilities;', getTailwindUtilities())
  
  return processed
}

/**
 * Process PostCSS
 */
async function processPostCSS(content: string, build: BuildContext): Promise<string> {
  // Simplified PostCSS processing
  // In a real implementation, you'd use the actual PostCSS processor
  
  let processed = content
  
  // Autoprefixer simulation
  processed = addVendorPrefixes(processed)
  
  // CSS nesting support
  processed = processNestedCSS(processed)
  
  return processed
}

/**
 * Compile SCSS to CSS
 */
async function compileSCSS(content: string, filePath: string): Promise<string> {
  console.log('💎 Compiling SCSS...')
  
  // This is a very simplified SCSS compilation
  // In a real implementation, you'd use sass or node-sass
  
  let compiled = content
  
  // Process variables (very basic)
  compiled = processSCSSVariables(compiled)
  
  // Process nesting (very basic)
  compiled = processSCSSNesting(compiled)
  
  // Process mixins (very basic)
  compiled = processSCSSMixins(compiled)
  
  return compiled
}

/**
 * Process CSS Modules
 */
async function processCSSModules(content: string, filePath: string): Promise<{ css: string, classNames: Record<string, string> }> {
  console.log('📦 Processing CSS Modules...')
  
  const classNames: Record<string, string> = {}
  let css = content
  
  // Extract class names and generate unique identifiers
  const classRegex = /\.([a-zA-Z_-][a-zA-Z0-9_-]*)/g
  let match
  
  while ((match = classRegex.exec(content)) !== null) {
    const originalClass = match[1]
    const hashedClass = generateHashedClassName(originalClass, filePath)
    
    classNames[originalClass] = hashedClass
    css = css.replace(new RegExp(`\\.${originalClass}\\b`, 'g'), `.${hashedClass}`)
  }
  
  return { css, classNames }
}

/**
 * Minify CSS
 */
function minifyCSS(content: string): string {
  return content
    // Remove comments
    .replace(/\/\*[\s\S]*?\*\//g, '')
    // Remove unnecessary whitespace
    .replace(/\s+/g, ' ')
    // Remove whitespace around certain characters
    .replace(/\s*([{}:;,>+~])\s*/g, '$1')
    // Remove trailing semicolons
    .replace(/;}/g, '}')
    // Remove leading/trailing whitespace
    .trim()
}

/**
 * Add vendor prefixes
 */
function addVendorPrefixes(content: string): string {
  // Simplified autoprefixer
  const prefixMap: Record<string, string[]> = {
    'transform': ['-webkit-transform', '-moz-transform', '-ms-transform'],
    'transition': ['-webkit-transition', '-moz-transition', '-ms-transition'],
    'border-radius': ['-webkit-border-radius', '-moz-border-radius'],
    'box-shadow': ['-webkit-box-shadow', '-moz-box-shadow'],
    'user-select': ['-webkit-user-select', '-moz-user-select', '-ms-user-select'],
  }
  
  let processed = content
  
  for (const [property, prefixes] of Object.entries(prefixMap)) {
    const regex = new RegExp(`(\\s|^)${property}\\s*:`, 'g')
    processed = processed.replace(regex, (match) => {
      const prefixedProperties = prefixes.map(prefix => match.replace(property, prefix)).join('\n  ')
      return prefixedProperties + '\n  ' + match
    })
  }
  
  return processed
}

/**
 * Process nested CSS
 */
function processNestedCSS(content: string): string {
  // Very basic CSS nesting support
  // In a real implementation, you'd use a proper CSS parser
  return content
}

/**
 * Process SCSS variables
 */
function processSCSSVariables(content: string): string {
  const variables: Record<string, string> = {}
  
  // Extract variables
  const variableRegex = /\$([a-zA-Z_-][a-zA-Z0-9_-]*)\s*:\s*([^;]+);/g
  let match
  
  while ((match = variableRegex.exec(content)) !== null) {
    variables[match[1]] = match[2].trim()
  }
  
  // Replace variable usage
  let processed = content
  for (const [name, value] of Object.entries(variables)) {
    const regex = new RegExp(`\\$${name}\\b`, 'g')
    processed = processed.replace(regex, value)
  }
  
  // Remove variable declarations
  processed = processed.replace(/\$[a-zA-Z_-][a-zA-Z0-9_-]*\s*:\s*[^;]+;/g, '')
  
  return processed
}

/**
 * Process SCSS nesting
 */
function processSCSSNesting(content: string): string {
  // Very basic nesting support
  // In a real implementation, you'd use a proper SCSS parser
  return content
}

/**
 * Process SCSS mixins
 */
function processSCSSMixins(content: string): string {
  // Very basic mixin support
  // In a real implementation, you'd use a proper SCSS parser
  return content
}

/**
 * Generate hashed class name for CSS Modules
 */
function generateHashedClassName(originalClass: string, filePath: string): string {
  // Simple hash generation
  const hash = Buffer.from(filePath + originalClass).toString('base64').slice(0, 8).replace(/[^a-zA-Z0-9]/g, '')
  return `${originalClass}_${hash}`
}

/**
 * Get Tailwind base styles
 */
function getTailwindBase(): string {
  return `
/* Tailwind CSS Base Styles */
*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

body {
  margin: 0;
  line-height: inherit;
}
`
}

/**
 * Get Tailwind component styles
 */
function getTailwindComponents(): string {
  return `
/* Tailwind CSS Components */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}
`
}

/**
 * Get Tailwind utility styles
 */
function getTailwindUtilities(): string {
  return `
/* Tailwind CSS Utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: 400; }

.text-white { color: #ffffff; }
.text-black { color: #000000; }
.text-gray-500 { color: #6b7280; }

.bg-white { background-color: #ffffff; }
.bg-black { background-color: #000000; }
.bg-gray-100 { background-color: #f3f4f6; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.m-4 { margin: 1rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }
`
}
