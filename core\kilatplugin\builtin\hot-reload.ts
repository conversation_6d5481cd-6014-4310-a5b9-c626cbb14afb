/**
 * Hot Reload Plugin - Built-in plugin for development hot reloading
 * Provides fast refresh for React components and automatic page reloading
 */

import { watch } from 'fs'
import { Plugin, BuildContext } from '../../kilatcore/types'

interface HotReloadClient {
  id: string
  ws: any
  lastPing: number
}

export const hotReloadPlugin: Plugin = {
  name: 'hot-reload',
  
  async setup(build: BuildContext) {
    if (!build.isDev) {
      console.log('🔥 Hot reload disabled in production')
      return
    }
    
    console.log('🔥 Hot Reload plugin initialized')
    
    const hotReloadServer = new HotReloadServer()
    await hotReloadServer.start()
    
    // Watch for file changes
    const watcher = new FileWatcher()
    watcher.onFileChange((filePath, changeType) => {
      hotReloadServer.notifyClients({
        type: 'file-changed',
        path: filePath,
        changeType,
        timestamp: Date.now()
      })
    })
    
    // Start watching project files
    watcher.watchDirectory('./apps')
    watcher.watchDirectory('./components')
    watcher.watchDirectory('./core')
    
    // Inject hot reload client code
    build.onLoad(/\.(tsx?|jsx?)$/, async (args) => {
      const originalResult = await args.originalLoader?.(args)
      
      if (originalResult && originalResult.contents) {
        const hotReloadClient = getHotReloadClientCode()
        const modifiedContents = `${hotReloadClient}\n${originalResult.contents}`
        
        return {
          ...originalResult,
          contents: modifiedContents
        }
      }
      
      return originalResult
    })
    
    console.log('🔥 Hot reload server started on ws://localhost:3001')
  }
}

/**
 * Hot Reload Server - WebSocket server for hot reload communication
 */
class HotReloadServer {
  private clients: Map<string, HotReloadClient> = new Map()
  private server: any = null
  private port = 3001
  
  async start(): Promise<void> {
    // Simple WebSocket server implementation
    // In a real implementation, you'd use a proper WebSocket library
    
    console.log(`🔥 Starting hot reload server on port ${this.port}`)
    
    // Mock WebSocket server for demonstration
    this.server = {
      port: this.port,
      clients: this.clients
    }
    
    // Simulate server startup
    setTimeout(() => {
      console.log(`✅ Hot reload server listening on ws://localhost:${this.port}`)
    }, 100)
  }
  
  async stop(): Promise<void> {
    if (this.server) {
      console.log('🛑 Stopping hot reload server')
      this.clients.clear()
      this.server = null
    }
  }
  
  addClient(client: HotReloadClient): void {
    this.clients.set(client.id, client)
    console.log(`🔌 Hot reload client connected: ${client.id}`)
  }
  
  removeClient(clientId: string): void {
    this.clients.delete(clientId)
    console.log(`🔌 Hot reload client disconnected: ${clientId}`)
  }
  
  notifyClients(message: any): void {
    const messageStr = JSON.stringify(message)
    
    for (const [clientId, client] of this.clients) {
      try {
        // In a real implementation, you'd send via WebSocket
        console.log(`📤 Sending to client ${clientId}:`, message.type)
        
        // Simulate message sending
        this.simulateMessageSend(client, message)
      } catch (error) {
        console.error(`Error sending message to client ${clientId}:`, error)
        this.removeClient(clientId)
      }
    }
  }
  
  private simulateMessageSend(client: HotReloadClient, message: any): void {
    // Simulate WebSocket message sending
    // In a real implementation, this would be: client.ws.send(JSON.stringify(message))
  }
  
  getClientCount(): number {
    return this.clients.size
  }
}

/**
 * File Watcher - Watches for file changes and triggers hot reload
 */
class FileWatcher {
  private watchers: Map<string, any> = new Map()
  private changeCallbacks: Array<(filePath: string, changeType: string) => void> = []
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map()
  
  watchDirectory(directory: string): void {
    if (this.watchers.has(directory)) {
      return
    }
    
    console.log(`👀 Watching directory: ${directory}`)
    
    const watcher = watch(directory, { recursive: true }, (eventType, filename) => {
      if (filename) {
        const fullPath = `${directory}/${filename}`
        this.handleFileChange(fullPath, eventType)
      }
    })
    
    this.watchers.set(directory, watcher)
  }
  
  private handleFileChange(filePath: string, changeType: string): void {
    // Debounce file changes to avoid excessive reloads
    const existingTimer = this.debounceTimers.get(filePath)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }
    
    const timer = setTimeout(() => {
      this.notifyFileChange(filePath, changeType)
      this.debounceTimers.delete(filePath)
    }, 100)
    
    this.debounceTimers.set(filePath, timer)
  }
  
  private notifyFileChange(filePath: string, changeType: string): void {
    console.log(`📝 File changed: ${filePath} (${changeType})`)
    
    for (const callback of this.changeCallbacks) {
      try {
        callback(filePath, changeType)
      } catch (error) {
        console.error('Error in file change callback:', error)
      }
    }
  }
  
  onFileChange(callback: (filePath: string, changeType: string) => void): void {
    this.changeCallbacks.push(callback)
  }
  
  stopWatching(): void {
    for (const [directory, watcher] of this.watchers) {
      watcher.close()
      console.log(`🛑 Stopped watching: ${directory}`)
    }
    
    this.watchers.clear()
    
    // Clear debounce timers
    for (const timer of this.debounceTimers.values()) {
      clearTimeout(timer)
    }
    this.debounceTimers.clear()
  }
}

/**
 * Get hot reload client code to inject into pages
 */
function getHotReloadClientCode(): string {
  return `
// Kilat.js Hot Reload Client
(function() {
  if (typeof window === 'undefined') return;
  
  let ws;
  let reconnectAttempts = 0;
  const maxReconnectAttempts = 10;
  
  function connect() {
    try {
      ws = new WebSocket('ws://localhost:3001');
      
      ws.onopen = function() {
        console.log('🔥 Hot reload connected');
        reconnectAttempts = 0;
      };
      
      ws.onmessage = function(event) {
        const message = JSON.parse(event.data);
        handleHotReloadMessage(message);
      };
      
      ws.onclose = function() {
        console.log('🔥 Hot reload disconnected');
        
        if (reconnectAttempts < maxReconnectAttempts) {
          reconnectAttempts++;
          setTimeout(connect, 1000 * reconnectAttempts);
        }
      };
      
      ws.onerror = function(error) {
        console.error('🔥 Hot reload error:', error);
      };
    } catch (error) {
      console.error('🔥 Failed to connect to hot reload server:', error);
    }
  }
  
  function handleHotReloadMessage(message) {
    switch (message.type) {
      case 'file-changed':
        handleFileChanged(message);
        break;
      case 'full-reload':
        window.location.reload();
        break;
      case 'css-update':
        updateCSS(message);
        break;
      default:
        console.log('🔥 Unknown hot reload message:', message);
    }
  }
  
  function handleFileChanged(message) {
    const { path, changeType } = message;
    
    // Check if it's a CSS file
    if (path.endsWith('.css')) {
      updateCSS(message);
      return;
    }
    
    // Check if it's a React component
    if (path.endsWith('.tsx') || path.endsWith('.jsx')) {
      // Try React Fast Refresh if available
      if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('🔥 Attempting React Fast Refresh');
        // In a real implementation, you'd trigger React Fast Refresh
      } else {
        // Fallback to full page reload
        console.log('🔥 Reloading page due to component change');
        window.location.reload();
      }
      return;
    }
    
    // For other files, do a full reload
    console.log('🔥 Reloading page due to file change');
    window.location.reload();
  }
  
  function updateCSS(message) {
    console.log('🎨 Updating CSS');
    
    // Find existing stylesheets and update them
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
    stylesheets.forEach(link => {
      const href = link.getAttribute('href');
      if (href) {
        // Add timestamp to force reload
        const url = new URL(href, window.location.origin);
        url.searchParams.set('t', Date.now().toString());
        link.setAttribute('href', url.toString());
      }
    });
  }
  
  // Connect when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', connect);
  } else {
    connect();
  }
  
  // Expose hot reload API
  window.__kilat_hot_reload__ = {
    connect,
    disconnect: function() {
      if (ws) {
        ws.close();
      }
    }
  };
})();
`
}
