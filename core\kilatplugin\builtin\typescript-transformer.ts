/**
 * TypeScript Transformer Plugin - Built-in plugin for TypeScript/JSX processing
 * Handles TypeScript compilation, JSX transformation, and type checking
 */

import { readFile } from 'fs/promises'
import { Plugin, BuildContext } from '../../kilatcore/types'

export const typescriptTransformerPlugin: Plugin = {
  name: 'typescript-transformer',
  
  async setup(build: BuildContext) {
    console.log('📝 TypeScript Transformer plugin initialized')
    
    // Handle TypeScript files
    build.onLoad(/\.tsx?$/, async (args) => {
      const content = await readFile(args.path, 'utf-8')
      
      let transformedContent = content
      
      // Transform TypeScript to JavaScript
      transformedContent = await transformTypeScript(transformedContent, args.path, build)
      
      // Transform JSX if present
      if (args.path.endsWith('.tsx') || content.includes('jsx')) {
        transformedContent = await transformJSX(transformedContent, build)
      }
      
      // Remove TypeScript-specific syntax
      transformedContent = removeTypeScriptSyntax(transformedContent)
      
      return {
        contents: transformedContent,
        loader: 'js'
      }
    })
    
    // Handle JSX files
    build.onLoad(/\.jsx$/, async (args) => {
      const content = await readFile(args.path, 'utf-8')
      
      const transformedContent = await transformJSX(content, build)
      
      return {
        contents: transformedContent,
        loader: 'js'
      }
    })
    
    // Handle import resolution for TypeScript paths
    build.onResolve(/.*/, async (args) => {
      // Handle @/ imports
      if (args.path.startsWith('@/')) {
        const resolvedPath = args.path.replace('@/', './')
        return {
          path: resolvedPath,
          namespace: 'file'
        }
      }
      
      // Handle TypeScript file extensions
      if (args.path.endsWith('.ts') || args.path.endsWith('.tsx')) {
        return {
          path: args.path,
          namespace: 'file'
        }
      }
      
      return null
    })
  }
}

/**
 * Transform TypeScript to JavaScript
 */
async function transformTypeScript(content: string, filePath: string, build: BuildContext): Promise<string> {
  console.log(`🔄 Transforming TypeScript: ${filePath}`)
  
  let transformed = content
  
  // Remove type annotations
  transformed = removeTypeAnnotations(transformed)
  
  // Transform interfaces and types
  transformed = removeInterfacesAndTypes(transformed)
  
  // Transform enums
  transformed = transformEnums(transformed)
  
  // Transform decorators
  transformed = transformDecorators(transformed)
  
  // Transform import/export syntax
  transformed = transformImportExport(transformed)
  
  return transformed
}

/**
 * Transform JSX to JavaScript
 */
async function transformJSX(content: string, build: BuildContext): Promise<string> {
  console.log('⚛️ Transforming JSX...')
  
  let transformed = content
  
  // Add React import if not present
  if (!transformed.includes('import React') && !transformed.includes('import * as React')) {
    transformed = `import React from 'react';\n${transformed}`
  }
  
  // Transform JSX elements
  transformed = transformJSXElements(transformed)
  
  // Transform JSX fragments
  transformed = transformJSXFragments(transformed)
  
  // Transform JSX expressions
  transformed = transformJSXExpressions(transformed)
  
  return transformed
}

/**
 * Remove TypeScript-specific syntax
 */
function removeTypeScriptSyntax(content: string): string {
  let cleaned = content
  
  // Remove type assertions
  cleaned = cleaned.replace(/\s+as\s+\w+/g, '')
  cleaned = cleaned.replace(/<\w+>/g, '')
  
  // Remove non-null assertions
  cleaned = cleaned.replace(/!/g, '')
  
  // Remove optional chaining for older browsers (if needed)
  // cleaned = cleaned.replace(/\?\./g, '.')
  
  return cleaned
}

/**
 * Remove type annotations
 */
function removeTypeAnnotations(content: string): string {
  let cleaned = content
  
  // Remove function parameter types
  cleaned = cleaned.replace(/(\w+)\s*:\s*[^,)=]+/g, '$1')
  
  // Remove function return types
  cleaned = cleaned.replace(/\)\s*:\s*[^{;]+/g, ')')
  
  // Remove variable type annotations
  cleaned = cleaned.replace(/:\s*[^=;,)}\]]+(?=[=;,)}\]])/g, '')
  
  return cleaned
}

/**
 * Remove interfaces and type definitions
 */
function removeInterfacesAndTypes(content: string): string {
  let cleaned = content
  
  // Remove interface declarations
  cleaned = cleaned.replace(/interface\s+\w+\s*{[^}]*}/gs, '')
  
  // Remove type declarations
  cleaned = cleaned.replace(/type\s+\w+\s*=\s*[^;]+;/gs, '')
  
  // Remove export type/interface
  cleaned = cleaned.replace(/export\s+(interface|type)\s+\w+[^;{]*[;{][^}]*}?/gs, '')
  
  return cleaned
}

/**
 * Transform enums
 */
function transformEnums(content: string): string {
  let transformed = content
  
  // Simple enum transformation
  const enumRegex = /enum\s+(\w+)\s*{([^}]*)}/gs
  
  transformed = transformed.replace(enumRegex, (match, enumName, enumBody) => {
    const members = enumBody.split(',').map(member => member.trim()).filter(Boolean)
    const enumObject = members.map((member, index) => {
      const [key, value] = member.split('=').map(s => s.trim())
      const enumValue = value || index.toString()
      return `  ${key}: ${enumValue}`
    }).join(',\n')
    
    return `const ${enumName} = {\n${enumObject}\n};`
  })
  
  return transformed
}

/**
 * Transform decorators
 */
function transformDecorators(content: string): string {
  // Simple decorator removal (in a real implementation, you'd transform them properly)
  return content.replace(/@\w+(\([^)]*\))?\s*/g, '')
}

/**
 * Transform import/export syntax
 */
function transformImportExport(content: string): string {
  let transformed = content
  
  // Transform export default
  transformed = transformed.replace(/export\s+default\s+/g, 'module.exports = ')
  
  // Transform named exports
  transformed = transformed.replace(/export\s+{([^}]+)}/g, (match, exports) => {
    const exportList = exports.split(',').map(exp => exp.trim())
    return exportList.map(exp => `module.exports.${exp} = ${exp};`).join('\n')
  })
  
  // Transform export const/let/var
  transformed = transformed.replace(/export\s+(const|let|var)\s+(\w+)/g, '$1 $2')
  
  return transformed
}

/**
 * Transform JSX elements
 */
function transformJSXElements(content: string): string {
  let transformed = content
  
  // Transform self-closing tags
  transformed = transformed.replace(/<(\w+)([^>]*?)\/>/g, (match, tagName, props) => {
    const propsObj = parseJSXProps(props)
    return `React.createElement('${tagName}', ${propsObj})`
  })
  
  // Transform opening/closing tags
  transformed = transformed.replace(/<(\w+)([^>]*?)>(.*?)<\/\1>/gs, (match, tagName, props, children) => {
    const propsObj = parseJSXProps(props)
    const childrenCode = transformJSXChildren(children)
    return `React.createElement('${tagName}', ${propsObj}, ${childrenCode})`
  })
  
  return transformed
}

/**
 * Transform JSX fragments
 */
function transformJSXFragments(content: string): string {
  let transformed = content
  
  // Transform React fragments
  transformed = transformed.replace(/<React\.Fragment([^>]*)>(.*?)<\/React\.Fragment>/gs, (match, props, children) => {
    const propsObj = parseJSXProps(props)
    const childrenCode = transformJSXChildren(children)
    return `React.createElement(React.Fragment, ${propsObj}, ${childrenCode})`
  })
  
  // Transform short fragment syntax
  transformed = transformed.replace(/<>(.*?)<\/>/gs, (match, children) => {
    const childrenCode = transformJSXChildren(children)
    return `React.createElement(React.Fragment, null, ${childrenCode})`
  })
  
  return transformed
}

/**
 * Transform JSX expressions
 */
function transformJSXExpressions(content: string): string {
  // Transform JSX expressions in curly braces
  return content.replace(/{([^}]+)}/g, (match, expression) => {
    return expression.trim()
  })
}

/**
 * Parse JSX props
 */
function parseJSXProps(propsString: string): string {
  if (!propsString.trim()) return 'null'
  
  const props: string[] = []
  const propRegex = /(\w+)(?:=(?:{([^}]+)}|"([^"]+)"))?/g
  let match
  
  while ((match = propRegex.exec(propsString)) !== null) {
    const [, propName, jsValue, stringValue] = match
    
    if (jsValue) {
      props.push(`${propName}: ${jsValue}`)
    } else if (stringValue) {
      props.push(`${propName}: "${stringValue}"`)
    } else {
      props.push(`${propName}: true`)
    }
  }
  
  return props.length > 0 ? `{${props.join(', ')}}` : 'null'
}

/**
 * Transform JSX children
 */
function transformJSXChildren(children: string): string {
  if (!children.trim()) return ''
  
  // Split children by JSX elements and text
  const parts = children.split(/(<[^>]+>.*?<\/[^>]+>|<[^>]+\/>)/g)
  const transformedParts = parts
    .filter(part => part.trim())
    .map(part => {
      if (part.startsWith('<')) {
        // This is a JSX element, transform it
        return transformJSXElements(part)
      } else {
        // This is text content
        return `"${part.trim()}"`
      }
    })
  
  return transformedParts.join(', ')
}
