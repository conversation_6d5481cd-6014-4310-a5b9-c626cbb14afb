/**
 * Plugin Loader - Dynamic plugin loading and hot reload
 * Handles plugin discovery, loading, and hot reloading during development
 */

import { watch } from 'fs'
import { readdir, stat } from 'fs/promises'
import { join, extname } from 'path'
import { KilatConfig, Plugin, HotReloadEvent } from '../kilatcore/types'

export class PluginLoader {
  private config: KilatConfig
  private loadedPlugins: Map<string, Plugin> = new Map()
  private watchers: Map<string, any> = new Map()
  private hotReloadCallbacks: Array<(event: HotReloadEvent) => void> = []
  
  constructor(config: KilatConfig) {
    this.config = config
  }
  
  /**
   * Start plugin loading and watching
   */
  async start(): Promise<void> {
    console.log('🔌 Starting plugin loader...')
    
    // Load initial plugins
    await this.loadAllPlugins()
    
    // Setup hot reload in development
    if (process.env.NODE_ENV === 'development') {
      await this.setupHotReload()
    }
    
    console.log(`✅ Plugin loader started with ${this.loadedPlugins.size} plugins`)
  }
  
  /**
   * Stop plugin loader and cleanup watchers
   */
  async stop(): Promise<void> {
    console.log('🛑 Stopping plugin loader...')
    
    // Close all file watchers
    for (const [path, watcher] of this.watchers) {
      watcher.close()
    }
    this.watchers.clear()
    
    // Clear loaded plugins
    this.loadedPlugins.clear()
    
    console.log('✅ Plugin loader stopped')
  }
  
  /**
   * Load all plugins from configured directories
   */
  private async loadAllPlugins(): Promise<void> {
    const pluginDirs = [
      this.config.plugins.dir,
      join(process.cwd(), 'plugins'), // Additional plugins directory
    ]
    
    for (const dir of pluginDirs) {
      await this.loadPluginsFromDirectory(dir)
    }
  }
  
  /**
   * Load plugins from a specific directory
   */
  private async loadPluginsFromDirectory(dir: string): Promise<void> {
    try {
      await stat(dir)
      const pluginFiles = await this.scanPluginFiles(dir)
      
      for (const file of pluginFiles) {
        await this.loadPluginFile(file)
      }
    } catch (error) {
      // Directory doesn't exist, which is fine
      console.log(`Plugin directory not found: ${dir}`)
    }
  }
  
  /**
   * Scan for plugin files in directory
   */
  private async scanPluginFiles(dir: string): Promise<string[]> {
    const pluginFiles: string[] = []
    
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name)
        
        if (entry.isFile()) {
          const ext = extname(entry.name)
          if (['.ts', '.js'].includes(ext) && !entry.name.endsWith('.d.ts')) {
            pluginFiles.push(fullPath)
          }
        } else if (entry.isDirectory()) {
          // Look for index files in subdirectories
          const indexFiles = ['index.ts', 'index.js']
          for (const indexFile of indexFiles) {
            const indexPath = join(fullPath, indexFile)
            try {
              await stat(indexPath)
              pluginFiles.push(indexPath)
              break
            } catch {
              // Index file doesn't exist
            }
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning plugin files in ${dir}:`, error)
    }
    
    return pluginFiles
  }
  
  /**
   * Load a single plugin file
   */
  private async loadPluginFile(filePath: string): Promise<void> {
    try {
      // Clear module cache for hot reload
      if (require.cache[filePath]) {
        delete require.cache[filePath]
      }
      
      // Dynamic import the plugin
      const pluginModule = await import(filePath + '?t=' + Date.now())
      const plugin = pluginModule.default || pluginModule
      
      if (this.isValidPlugin(plugin)) {
        this.loadedPlugins.set(plugin.name, plugin)
        console.log(`🔌 Loaded plugin: ${plugin.name}`)
        
        // Notify hot reload listeners
        this.notifyHotReload({
          type: 'update',
          path: filePath,
          timestamp: Date.now()
        })
      } else {
        console.warn(`Invalid plugin format in ${filePath}`)
      }
    } catch (error) {
      console.error(`Failed to load plugin from ${filePath}:`, error)
    }
  }
  
  /**
   * Setup hot reload for development
   */
  private async setupHotReload(): Promise<void> {
    console.log('🔥 Setting up plugin hot reload...')
    
    const pluginDirs = [
      this.config.plugins.dir,
      join(process.cwd(), 'plugins'),
    ]
    
    for (const dir of pluginDirs) {
      try {
        await stat(dir)
        this.watchDirectory(dir)
      } catch {
        // Directory doesn't exist
      }
    }
  }
  
  /**
   * Watch directory for changes
   */
  private watchDirectory(dir: string): void {
    const watcher = watch(dir, { recursive: true }, (eventType, filename) => {
      if (filename && this.isPluginFile(filename)) {
        const fullPath = join(dir, filename)
        
        if (eventType === 'change') {
          console.log(`🔄 Plugin file changed: ${filename}`)
          this.reloadPlugin(fullPath)
        } else if (eventType === 'rename') {
          console.log(`📝 Plugin file renamed: ${filename}`)
          this.handlePluginRename(fullPath)
        }
      }
    })
    
    this.watchers.set(dir, watcher)
    console.log(`👀 Watching plugin directory: ${dir}`)
  }
  
  /**
   * Check if file is a plugin file
   */
  private isPluginFile(filename: string): boolean {
    const ext = extname(filename)
    return ['.ts', '.js'].includes(ext) && !filename.endsWith('.d.ts')
  }
  
  /**
   * Reload a plugin file
   */
  private async reloadPlugin(filePath: string): Promise<void> {
    try {
      await this.loadPluginFile(filePath)
      console.log(`🔄 Reloaded plugin: ${filePath}`)
    } catch (error) {
      console.error(`Failed to reload plugin ${filePath}:`, error)
    }
  }
  
  /**
   * Handle plugin file rename/delete
   */
  private async handlePluginRename(filePath: string): Promise<void> {
    try {
      await stat(filePath)
      // File exists, it was renamed to this path
      await this.loadPluginFile(filePath)
    } catch {
      // File doesn't exist, it was deleted or renamed away
      this.unloadPluginByPath(filePath)
    }
  }
  
  /**
   * Unload plugin by file path
   */
  private unloadPluginByPath(filePath: string): void {
    for (const [name, plugin] of this.loadedPlugins) {
      // In a real implementation, you'd track plugin file paths
      // For now, we'll just log the attempt
      console.log(`🗑️ Attempting to unload plugin from: ${filePath}`)
    }
  }
  
  /**
   * Validate plugin format
   */
  private isValidPlugin(plugin: any): plugin is Plugin {
    return (
      plugin &&
      typeof plugin === 'object' &&
      typeof plugin.name === 'string' &&
      typeof plugin.setup === 'function'
    )
  }
  
  /**
   * Get loaded plugin by name
   */
  getPlugin(name: string): Plugin | undefined {
    return this.loadedPlugins.get(name)
  }
  
  /**
   * Get all loaded plugins
   */
  getAllPlugins(): Plugin[] {
    return Array.from(this.loadedPlugins.values())
  }
  
  /**
   * Register hot reload callback
   */
  onHotReload(callback: (event: HotReloadEvent) => void): void {
    this.hotReloadCallbacks.push(callback)
  }
  
  /**
   * Notify hot reload listeners
   */
  private notifyHotReload(event: HotReloadEvent): void {
    for (const callback of this.hotReloadCallbacks) {
      try {
        callback(event)
      } catch (error) {
        console.error('Error in hot reload callback:', error)
      }
    }
  }
  
  /**
   * Get plugin count
   */
  getPluginCount(): number {
    return this.loadedPlugins.size
  }
  
  /**
   * Check if plugin is loaded
   */
  isPluginLoaded(name: string): boolean {
    return this.loadedPlugins.has(name)
  }
}
