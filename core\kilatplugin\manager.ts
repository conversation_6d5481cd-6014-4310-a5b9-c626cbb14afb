/**
 * Plugin Manager - Manages plugin loading and execution
 * Handles plugin lifecycle and dependency resolution
 */

import { readdir, stat } from 'fs/promises'
import { join, extname } from 'path'
import { KilatConfig, Plugin, BuildContext } from '../kilatcore/types'

export class PluginManager {
  private config: KilatConfig
  private plugins: Map<string, Plugin> = new Map()
  private loadedPlugins: Set<string> = new Set()
  
  constructor(config: KilatConfig) {
    this.config = config
  }
  
  /**
   * Load all plugins
   */
  async loadPlugins(): Promise<void> {
    console.log('🔌 Loading plugins...')
    
    if (!this.config.plugins.autoLoad) {
      console.log('Plugin auto-loading is disabled')
      return
    }
    
    // Load built-in plugins
    await this.loadBuiltinPlugins()
    
    // Load custom plugins from plugins directory
    await this.loadCustomPlugins()
    
    // Load development-specific plugins
    if (process.env.NODE_ENV === 'development') {
      await this.loadDevPlugins()
    }
    
    // Load build-specific plugins
    if (process.env.NODE_ENV === 'production') {
      await this.loadBuildPlugins()
    }
    
    console.log(`✅ Loaded ${this.plugins.size} plugins`)
  }
  
  /**
   * Load built-in plugins
   */
  private async loadBuiltinPlugins(): Promise<void> {
    const builtinPlugins = [
      'css-processor',
      'typescript-transformer',
      'asset-optimizer',
      'hot-reload',
    ]
    
    for (const pluginName of builtinPlugins) {
      try {
        const plugin = await this.loadBuiltinPlugin(pluginName)
        if (plugin) {
          this.registerPlugin(plugin)
        }
      } catch (error) {
        console.warn(`Failed to load built-in plugin ${pluginName}:`, error)
      }
    }
  }
  
  /**
   * Load a built-in plugin
   */
  private async loadBuiltinPlugin(name: string): Promise<Plugin | null> {
    // This would load built-in plugins from the framework
    // For now, we'll create mock plugins
    
    switch (name) {
      case 'css-processor':
        return {
          name: 'css-processor',
          setup: async (build: BuildContext) => {
            console.log('🎨 CSS Processor plugin loaded')
            // CSS processing logic would go here
          }
        }
        
      case 'typescript-transformer':
        return {
          name: 'typescript-transformer',
          setup: async (build: BuildContext) => {
            console.log('📝 TypeScript Transformer plugin loaded')
            // TypeScript transformation logic would go here
          }
        }
        
      case 'asset-optimizer':
        return {
          name: 'asset-optimizer',
          setup: async (build: BuildContext) => {
            console.log('🖼️ Asset Optimizer plugin loaded')
            // Asset optimization logic would go here
          }
        }
        
      case 'hot-reload':
        if (process.env.NODE_ENV === 'development') {
          return {
            name: 'hot-reload',
            setup: async (build: BuildContext) => {
              console.log('🔥 Hot Reload plugin loaded')
              // Hot reload logic would go here
            }
          }
        }
        return null
        
      default:
        return null
    }
  }
  
  /**
   * Load custom plugins from plugins directory
   */
  private async loadCustomPlugins(): Promise<void> {
    const pluginsDir = this.config.plugins.dir
    
    try {
      await stat(pluginsDir)
      await this.scanPluginsDirectory(pluginsDir)
    } catch (error) {
      // Plugins directory doesn't exist, which is fine
      console.log('No custom plugins directory found')
    }
  }
  
  /**
   * Scan plugins directory for plugin files
   */
  private async scanPluginsDirectory(dir: string): Promise<void> {
    try {
      const entries = await readdir(dir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = join(dir, entry.name)
        
        if (entry.isFile()) {
          const ext = extname(entry.name)
          if (['.ts', '.js'].includes(ext)) {
            await this.loadPluginFile(fullPath)
          }
        } else if (entry.isDirectory()) {
          // Check for index file in subdirectory
          const indexPath = join(fullPath, 'index.ts')
          try {
            await stat(indexPath)
            await this.loadPluginFile(indexPath)
          } catch {
            // No index file, skip
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning plugins directory ${dir}:`, error)
    }
  }
  
  /**
   * Load plugin from file
   */
  private async loadPluginFile(filePath: string): Promise<void> {
    try {
      // Dynamic import the plugin file
      const pluginModule = await import(filePath)
      const plugin = pluginModule.default || pluginModule
      
      if (this.isValidPlugin(plugin)) {
        this.registerPlugin(plugin)
      } else {
        console.warn(`Invalid plugin format in ${filePath}`)
      }
    } catch (error) {
      console.error(`Failed to load plugin from ${filePath}:`, error)
    }
  }
  
  /**
   * Load development-specific plugins
   */
  private async loadDevPlugins(): Promise<void> {
    for (const pluginName of this.config.plugins.devPlugins) {
      try {
        await this.loadPluginByName(pluginName)
      } catch (error) {
        console.warn(`Failed to load dev plugin ${pluginName}:`, error)
      }
    }
  }
  
  /**
   * Load build-specific plugins
   */
  private async loadBuildPlugins(): Promise<void> {
    for (const pluginName of this.config.plugins.buildPlugins) {
      try {
        await this.loadPluginByName(pluginName)
      } catch (error) {
        console.warn(`Failed to load build plugin ${pluginName}:`, error)
      }
    }
  }
  
  /**
   * Load plugin by name
   */
  private async loadPluginByName(name: string): Promise<void> {
    // This would resolve plugin names to actual plugin files
    // For now, we'll just log the attempt
    console.log(`Loading plugin: ${name}`)
  }
  
  /**
   * Register a plugin
   */
  registerPlugin(plugin: Plugin): void {
    if (this.plugins.has(plugin.name)) {
      console.warn(`Plugin ${plugin.name} is already registered`)
      return
    }
    
    this.plugins.set(plugin.name, plugin)
    console.log(`📦 Registered plugin: ${plugin.name}`)
  }
  
  /**
   * Unregister a plugin
   */
  unregisterPlugin(name: string): void {
    if (this.plugins.has(name)) {
      this.plugins.delete(name)
      this.loadedPlugins.delete(name)
      console.log(`🗑️ Unregistered plugin: ${name}`)
    }
  }
  
  /**
   * Execute plugins for build context
   */
  async executePlugins(buildContext: BuildContext): Promise<void> {
    console.log('🔧 Executing plugins...')
    
    for (const [name, plugin] of this.plugins) {
      if (!this.loadedPlugins.has(name)) {
        try {
          await plugin.setup(buildContext)
          this.loadedPlugins.add(name)
          console.log(`✅ Executed plugin: ${name}`)
        } catch (error) {
          console.error(`❌ Failed to execute plugin ${name}:`, error)
        }
      }
    }
  }
  
  /**
   * Get plugin by name
   */
  getPlugin(name: string): Plugin | undefined {
    return this.plugins.get(name)
  }
  
  /**
   * Get all plugins
   */
  getAllPlugins(): Plugin[] {
    return Array.from(this.plugins.values())
  }
  
  /**
   * Check if plugin is valid
   */
  private isValidPlugin(plugin: any): plugin is Plugin {
    return (
      plugin &&
      typeof plugin === 'object' &&
      typeof plugin.name === 'string' &&
      typeof plugin.setup === 'function'
    )
  }
  
  /**
   * Get plugin count
   */
  getPluginCount(): number {
    return this.plugins.size
  }
  
  /**
   * Check if plugin is loaded
   */
  isPluginLoaded(name: string): boolean {
    return this.loadedPlugins.has(name)
  }
  
  /**
   * Clear all plugins
   */
  clearPlugins(): void {
    this.plugins.clear()
    this.loadedPlugins.clear()
  }
}
