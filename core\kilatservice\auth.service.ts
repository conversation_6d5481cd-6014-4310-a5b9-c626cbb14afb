/**
 * AuthService - Authentication business logic
 * Handles user authentication, session management, and security
 */

import { KilatORM } from '@/core/kilatorm'
import { KilatCache } from '@/core/kilatcache'
import { generateId } from '@/core/kilatlib/utils'

interface User {
  id: string
  email: string
  name: string
  role: string
  createdAt: Date
  updatedAt: Date
}

interface LoginResult {
  success: boolean
  user?: User
  token?: string
  error?: string
}

interface RegisterData {
  email: string
  password: string
  name: string
  role?: string
}

export class AuthService {
  private orm: KilatORM
  private cache: KilatCache
  
  constructor() {
    this.orm = new KilatORM()
    this.cache = new KilatCache()
  }
  
  /**
   * Login user with email and password
   */
  async login(email: string, password: string): Promise<LoginResult> {
    try {
      // Find user by email
      const user = await this.orm.users.findOne({ email })
      
      if (!user) {
        return {
          success: false,
          error: 'Invalid email or password'
        }
      }
      
      // Verify password (in real implementation, use bcrypt)
      const isValidPassword = await this.verifyPassword(password, user.passwordHash)
      
      if (!isValidPassword) {
        return {
          success: false,
          error: 'Invalid email or password'
        }
      }
      
      // Generate session token
      const token = this.generateToken()
      
      // Store session in cache
      await this.cache.set(`session:${token}`, {
        userId: user.id,
        email: user.email,
        role: user.role,
        createdAt: new Date()
      }, 24 * 60 * 60) // 24 hours
      
      // Remove password from response
      const { passwordHash, ...userWithoutPassword } = user
      
      return {
        success: true,
        user: userWithoutPassword,
        token
      }
      
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: 'Login failed'
      }
    }
  }
  
  /**
   * Register new user
   */
  async register(data: RegisterData): Promise<LoginResult> {
    try {
      // Check if user already exists
      const existingUser = await this.orm.users.findOne({ email: data.email })
      
      if (existingUser) {
        return {
          success: false,
          error: 'User with this email already exists'
        }
      }
      
      // Hash password (in real implementation, use bcrypt)
      const passwordHash = await this.hashPassword(data.password)
      
      // Create user
      const user = await this.orm.users.create({
        id: generateId('user'),
        email: data.email,
        name: data.name,
        role: data.role || 'user',
        passwordHash,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      // Generate session token
      const token = this.generateToken()
      
      // Store session in cache
      await this.cache.set(`session:${token}`, {
        userId: user.id,
        email: user.email,
        role: user.role,
        createdAt: new Date()
      }, 24 * 60 * 60) // 24 hours
      
      // Remove password from response
      const { passwordHash, ...userWithoutPassword } = user
      
      return {
        success: true,
        user: userWithoutPassword,
        token
      }
      
    } catch (error) {
      console.error('Register error:', error)
      return {
        success: false,
        error: 'Registration failed'
      }
    }
  }
  
  /**
   * Logout user by invalidating token
   */
  async logout(token: string): Promise<void> {
    try {
      await this.cache.delete(`session:${token}`)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }
  
  /**
   * Get current user from token
   */
  async getCurrentUser(token: string): Promise<User | null> {
    try {
      const session = await this.cache.get(`session:${token}`)
      
      if (!session) {
        return null
      }
      
      const user = await this.orm.users.findOne({ id: session.userId })
      
      if (!user) {
        // Clean up invalid session
        await this.cache.delete(`session:${token}`)
        return null
      }
      
      // Remove password from response
      const { passwordHash, ...userWithoutPassword } = user
      return userWithoutPassword
      
    } catch (error) {
      console.error('Get current user error:', error)
      return null
    }
  }
  
  /**
   * Verify if token is valid
   */
  async verifyToken(token: string): Promise<boolean> {
    try {
      const session = await this.cache.get(`session:${token}`)
      return !!session
    } catch (error) {
      console.error('Verify token error:', error)
      return false
    }
  }
  
  /**
   * Generate secure token
   */
  private generateToken(): string {
    return generateId('token') + '_' + Date.now() + '_' + Math.random().toString(36)
  }
  
  /**
   * Hash password (placeholder - use bcrypt in real implementation)
   */
  private async hashPassword(password: string): Promise<string> {
    // In real implementation, use bcrypt
    return `hashed_${password}_${Date.now()}`
  }
  
  /**
   * Verify password (placeholder - use bcrypt in real implementation)
   */
  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    // In real implementation, use bcrypt.compare
    return hash === `hashed_${password}_${Date.now()}` || hash.includes(password)
  }
}
