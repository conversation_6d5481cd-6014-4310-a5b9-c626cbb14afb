/**
 * OrdersService - Business logic for orders
 * Handles CRUD operations and business rules
 */

import { KilatORM } from '@/core/kilatorm'
import { generateId } from '@/core/kilatlib/utils'

interface Orders {
  id: string
  name: string
  createdAt: Date
  updatedAt: Date
}

interface CreateOrdersData {
  name: string
}

interface UpdateOrdersData {
  name?: string
}

interface GetAllOptions {
  page: number
  limit: number
  search?: string
}

interface GetAllResult {
  items: Orders[]
  total: number
}

interface ServiceResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

export class OrdersService {
  private orm: KilatORM
  
  constructor() {
    this.orm = new KilatORM()
  }
  
  /**
   * Get all orders with pagination
   */
  async getAll(options: GetAllOptions): Promise<GetAllResult> {
    try {
      const { page, limit, search } = options
      const offset = (page - 1) * limit
      
      let query: any = {}
      
      if (search) {
        query = {
          name: { $regex: search, $options: 'i' }
        }
      }
      
      const [items, total] = await Promise.all([
        this.orm.orders.find(query)
          .skip(offset)
          .limit(limit)
          .sort({ createdAt: -1 }),
        this.orm.orders.countDocuments(query)
      ])
      
      return { items, total }
      
    } catch (error) {
      console.error('Get all orders error:', error)
      return { items: [], total: 0 }
    }
  }
  
  /**
   * Get orders by ID
   */
  async getById(id: string): Promise<Orders | null> {
    try {
      return await this.orm.orders.findById(id)
    } catch (error) {
      console.error('Get orders by ID error:', error)
      return null
    }
  }
  
  /**
   * Create new orders
   */
  async create(data: CreateOrdersData): Promise<ServiceResult> {
    try {
      const orders = await this.orm.orders.create({
        id: generateId('orders'),
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      return {
        success: true,
        data: orders
      }
      
    } catch (error) {
      console.error('Create orders error:', error)
      return {
        success: false,
        error: 'Failed to create orders'
      }
    }
  }
  
  /**
   * Update orders
   */
  async update(id: string, data: UpdateOrdersData): Promise<ServiceResult> {
    try {
      const existing = await this.orm.orders.findById(id)
      
      if (!existing) {
        return {
          success: false,
          error: 'Orders not found'
        }
      }
      
      const updated = await this.orm.orders.updateOne(
        { id },
        {
          ...data,
          updatedAt: new Date()
        }
      )
      
      return {
        success: true,
        data: updated
      }
      
    } catch (error) {
      console.error('Update orders error:', error)
      return {
        success: false,
        error: 'Failed to update orders'
      }
    }
  }
  
  /**
   * Delete orders
   */
  async delete(id: string): Promise<ServiceResult> {
    try {
      const existing = await this.orm.orders.findById(id)
      
      if (!existing) {
        return {
          success: false,
          error: 'Orders not found'
        }
      }
      
      await this.orm.orders.deleteOne({ id })
      
      return {
        success: true
      }
      
    } catch (error) {
      console.error('Delete orders error:', error)
      return {
        success: false,
        error: 'Failed to delete orders'
      }
    }
  }
}
