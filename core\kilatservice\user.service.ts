/**
 * UserService - User management business logic
 * Handles CRUD operations for users
 */

import { KilatORM } from '@/core/kilatorm'
import { generateId } from '@/core/kilatlib/utils'

interface User {
  id: string
  email: string
  name: string
  role: string
  createdAt: Date
  updatedAt: Date
}

interface CreateUserData {
  name: string
  email: string
  role?: string
}

interface UpdateUserData {
  name?: string
  email?: string
  role?: string
}

interface GetUsersOptions {
  page: number
  limit: number
  search?: string
  role?: string
}

interface GetUsersResult {
  users: User[]
  total: number
}

interface ServiceResult<T = any> {
  success: boolean
  data?: T
  user?: User
  error?: string
}

export class UserService {
  private orm: KilatORM
  
  constructor() {
    this.orm = new KilatORM()
  }
  
  /**
   * Get users with pagination and filtering
   */
  async getUsers(options: GetUsersOptions): Promise<GetUsersResult> {
    try {
      const { page, limit, search, role } = options
      const offset = (page - 1) * limit
      
      let query: any = {}
      
      // Add search filter
      if (search) {
        query = {
          $or: [
            { name: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } }
          ]
        }
      }
      
      // Add role filter
      if (role) {
        query.role = role
      }
      
      const [users, total] = await Promise.all([
        this.orm.users.find(query)
          .skip(offset)
          .limit(limit)
          .sort({ createdAt: -1 }),
        this.orm.users.countDocuments(query)
      ])
      
      return {
        users: users.map(user => {
          const { passwordHash, ...userWithoutPassword } = user
          return userWithoutPassword
        }),
        total
      }
      
    } catch (error) {
      console.error('Get users error:', error)
      return {
        users: [],
        total: 0
      }
    }
  }
  
  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<User | null> {
    try {
      const user = await this.orm.users.findOne({ id })
      
      if (!user) {
        return null
      }
      
      const { passwordHash, ...userWithoutPassword } = user
      return userWithoutPassword
      
    } catch (error) {
      console.error('Get user by ID error:', error)
      return null
    }
  }
  
  /**
   * Create new user
   */
  async createUser(data: CreateUserData): Promise<ServiceResult> {
    try {
      // Check if user already exists
      const existingUser = await this.orm.users.findOne({ email: data.email })
      
      if (existingUser) {
        return {
          success: false,
          error: 'User with this email already exists'
        }
      }
      
      // Create user
      const user = await this.orm.users.create({
        id: generateId('user'),
        email: data.email,
        name: data.name,
        role: data.role || 'user',
        passwordHash: '', // Will be set during registration
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      const { passwordHash, ...userWithoutPassword } = user
      
      return {
        success: true,
        user: userWithoutPassword
      }
      
    } catch (error) {
      console.error('Create user error:', error)
      return {
        success: false,
        error: 'Failed to create user'
      }
    }
  }
  
  /**
   * Update user
   */
  async updateUser(id: string, data: UpdateUserData): Promise<ServiceResult> {
    try {
      // Check if user exists
      const existingUser = await this.orm.users.findOne({ id })
      
      if (!existingUser) {
        return {
          success: false,
          error: 'User not found'
        }
      }
      
      // Check if email is being changed and already exists
      if (data.email && data.email !== existingUser.email) {
        const emailExists = await this.orm.users.findOne({ email: data.email })
        if (emailExists) {
          return {
            success: false,
            error: 'Email already exists'
          }
        }
      }
      
      // Update user
      const updatedUser = await this.orm.users.updateOne(
        { id },
        {
          ...data,
          updatedAt: new Date()
        }
      )
      
      const { passwordHash, ...userWithoutPassword } = updatedUser
      
      return {
        success: true,
        user: userWithoutPassword
      }
      
    } catch (error) {
      console.error('Update user error:', error)
      return {
        success: false,
        error: 'Failed to update user'
      }
    }
  }
  
  /**
   * Delete user
   */
  async deleteUser(id: string): Promise<ServiceResult> {
    try {
      // Check if user exists
      const existingUser = await this.orm.users.findOne({ id })
      
      if (!existingUser) {
        return {
          success: false,
          error: 'User not found'
        }
      }
      
      // Delete user
      await this.orm.users.deleteOne({ id })
      
      return {
        success: true
      }
      
    } catch (error) {
      console.error('Delete user error:', error)
      return {
        success: false,
        error: 'Failed to delete user'
      }
    }
  }
  
  /**
   * Get user statistics
   */
  async getUserStats(): Promise<any> {
    try {
      const [totalUsers, activeUsers, usersByRole] = await Promise.all([
        this.orm.users.countDocuments({}),
        this.orm.users.countDocuments({ 
          lastLoginAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } 
        }),
        this.orm.users.aggregate([
          { $group: { _id: '$role', count: { $sum: 1 } } }
        ])
      ])
      
      return {
        totalUsers,
        activeUsers,
        usersByRole: usersByRole.reduce((acc, item) => {
          acc[item._id] = item.count
          return acc
        }, {})
      }
      
    } catch (error) {
      console.error('Get user stats error:', error)
      return {
        totalUsers: 0,
        activeUsers: 0,
        usersByRole: {}
      }
    }
  }
}
