/**
 * KilatState SSR - Server-side rendering and hydration utilities
 * Handles state serialization for SSR and client-side hydration
 */

import { getStore } from './store'
import { KilatConfig } from '../kilatcore/types'

interface SSRContext {
  url: string
  headers: Record<string, string>
  cookies: Record<string, string>
  userAgent?: string
}

interface HydrationOptions {
  partial?: boolean
  islands?: string[]
  priority?: 'high' | 'normal' | 'low'
}

export class KilatSSR {
  private config: KilatConfig
  
  constructor(config: KilatConfig) {
    this.config = config
  }
  
  /**
   * Serialize state for SSR
   */
  serializeState(): string {
    const store = getStore(this.config)
    const state = store.getAllState()
    
    try {
      return JSON.stringify(state)
    } catch (error) {
      console.error('Error serializing state for SSR:', error)
      return '{}'
    }
  }
  
  /**
   * Generate SSR state script tag
   */
  generateStateScript(): string {
    const serializedState = this.serializeState()
    
    return `
<script>
  window.__KILAT_STATE__ = ${serializedState};
  window.__KILAT_SSR__ = true;
</script>
`
  }
  
  /**
   * Prepare state for SSR context
   */
  async prepareSSRState(context: SSRContext): Promise<void> {
    const store = getStore(this.config)
    
    // Initialize state based on SSR context
    await this.initializeSSRState(store, context)
    
    console.log('🔄 KilatSSR: State prepared for SSR')
  }
  
  /**
   * Initialize state for SSR
   */
  private async initializeSSRState(store: any, context: SSRContext): Promise<void> {
    // Example: Initialize user state from cookies
    if (context.cookies.auth_token) {
      // Simulate user authentication
      const userData = await this.getUserFromToken(context.cookies.auth_token)
      if (userData) {
        store.dispatch('auth', 'setUser', userData)
      }
    }
    
    // Example: Initialize theme from user agent or cookies
    const theme = context.cookies.theme || this.detectThemeFromUserAgent(context.userAgent)
    if (theme) {
      store.dispatch('ui', 'setTheme', theme)
    }
    
    // Example: Initialize locale from headers
    const locale = this.detectLocaleFromHeaders(context.headers)
    if (locale) {
      store.dispatch('i18n', 'setLocale', locale)
    }
  }
  
  /**
   * Get user data from authentication token
   */
  private async getUserFromToken(token: string): Promise<any | null> {
    try {
      // Simulate token validation and user fetching
      // In a real implementation, this would validate the JWT and fetch user data
      return {
        id: 'user_123',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user'
      }
    } catch (error) {
      console.error('Error getting user from token:', error)
      return null
    }
  }
  
  /**
   * Detect theme from user agent
   */
  private detectThemeFromUserAgent(userAgent?: string): string {
    if (!userAgent) return 'glow'
    
    // Simple theme detection based on user agent
    if (userAgent.includes('Mobile')) {
      return 'glow' // Mobile-friendly theme
    }
    
    return 'glow' // Default theme
  }
  
  /**
   * Detect locale from request headers
   */
  private detectLocaleFromHeaders(headers: Record<string, string>): string {
    const acceptLanguage = headers['accept-language']
    if (!acceptLanguage) return 'en'
    
    // Parse Accept-Language header
    const languages = acceptLanguage.split(',').map(lang => {
      const [code, quality] = lang.trim().split(';q=')
      return {
        code: code.split('-')[0], // Get language code without region
        quality: quality ? parseFloat(quality) : 1.0
      }
    })
    
    // Sort by quality and return the highest
    languages.sort((a, b) => b.quality - a.quality)
    return languages[0]?.code || 'en'
  }
  
  /**
   * Setup client-side hydration
   */
  setupHydration(options: HydrationOptions = {}): string {
    const hydrationMode = this.config.state.hydration
    
    return `
<script>
(function() {
  // Kilat.js State Hydration
  const hydrationMode = '${hydrationMode}';
  const options = ${JSON.stringify(options)};
  
  function hydrateState() {
    if (typeof window === 'undefined' || !window.__KILAT_STATE__) {
      return;
    }
    
    console.log('🔄 KilatState: Starting hydration (' + hydrationMode + ')');
    
    switch (hydrationMode) {
      case 'full':
        hydrateFullState();
        break;
      case 'partial':
        hydratePartialState(options);
        break;
      case 'islands':
        hydrateIslands(options);
        break;
      default:
        console.warn('Unknown hydration mode:', hydrationMode);
    }
  }
  
  function hydrateFullState() {
    // Full hydration - hydrate all state immediately
    console.log('🔄 Full state hydration');
    
    // The store will automatically pick up window.__KILAT_STATE__
    // when it's initialized on the client
  }
  
  function hydratePartialState(options) {
    // Partial hydration - hydrate only critical state
    console.log('🔄 Partial state hydration');
    
    if (options.priority === 'high') {
      // Hydrate high-priority state immediately
      hydrateHighPriorityState();
    } else {
      // Defer non-critical state hydration
      requestIdleCallback(hydrateNonCriticalState);
    }
  }
  
  function hydrateIslands(options) {
    // Islands hydration - hydrate specific components/islands
    console.log('🔄 Islands state hydration');
    
    const islands = options.islands || [];
    islands.forEach(island => {
      hydrateIsland(island);
    });
  }
  
  function hydrateHighPriorityState() {
    // Hydrate critical state like auth, theme, etc.
    const criticalSlices = ['auth', 'ui', 'router'];
    console.log('🔄 Hydrating critical state:', criticalSlices);
  }
  
  function hydrateNonCriticalState() {
    // Hydrate non-critical state
    console.log('🔄 Hydrating non-critical state');
  }
  
  function hydrateIsland(islandId) {
    // Hydrate specific island/component
    console.log('🔄 Hydrating island:', islandId);
    
    const element = document.getElementById(islandId);
    if (element) {
      // Trigger hydration for this specific component
      element.setAttribute('data-hydrated', 'true');
    }
  }
  
  // Start hydration when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', hydrateState);
  } else {
    hydrateState();
  }
  
  // Expose hydration API
  window.__kilat_hydration__ = {
    hydrateState,
    hydrateIsland,
    isHydrated: function() {
      return window.__KILAT_SSR__ === true;
    }
  };
})();
</script>
`
  }
  
  /**
   * Generate preload state for critical resources
   */
  generatePreloadState(): string {
    const store = getStore(this.config)
    const state = store.getAllState()
    
    // Extract critical state that should be preloaded
    const criticalState = {
      auth: state.auth || null,
      ui: state.ui || null,
      router: state.router || null
    }
    
    return `
<script>
  window.__KILAT_PRELOAD__ = ${JSON.stringify(criticalState)};
</script>
`
  }
  
  /**
   * Check if running in SSR context
   */
  static isSSR(): boolean {
    return typeof window === 'undefined'
  }
  
  /**
   * Check if client is hydrated
   */
  static isHydrated(): boolean {
    return typeof window !== 'undefined' && (window as any).__KILAT_SSR__ === true
  }
  
  /**
   * Get hydration stats
   */
  getHydrationStats(): {
    isSSR: boolean
    isHydrated: boolean
    hydrationMode: string
    stateSize: number
  } {
    const store = getStore(this.config)
    const stats = store.getStats()
    
    return {
      isSSR: KilatSSR.isSSR(),
      isHydrated: KilatSSR.isHydrated(),
      hydrationMode: this.config.state.hydration,
      stateSize: stats.stateSize
    }
  }
}

/**
 * Create SSR instance
 */
export function createSSR(config: KilatConfig): KilatSSR {
  return new KilatSSR(config)
}

/**
 * Utility function to wrap component with hydration boundary
 */
export function withHydration<T extends React.ComponentType<any>>(
  Component: T,
  options: HydrationOptions = {}
): T {
  const WrappedComponent = (props: any) => {
    const [isHydrated, setIsHydrated] = React.useState(false)
    
    React.useEffect(() => {
      setIsHydrated(true)
    }, [])
    
    if (!isHydrated && typeof window !== 'undefined') {
      // Return loading state or SSR content
      return React.createElement('div', { 
        'data-hydration-boundary': true,
        'data-hydration-pending': true 
      }, 'Loading...')
    }
    
    return React.createElement(Component, props)
  }
  
  WrappedComponent.displayName = `withHydration(${Component.displayName || Component.name})`
  
  return WrappedComponent as T
}
