/**
 * KilatState Store - Global state management for Kilat.js
 * SSR-aware state management with automatic hydration
 */

import { KilatConfig } from '../kilatcore/types'

interface StateListener<T = any> {
  (newState: T, oldState: T): void
}

interface StateSlice<T = any> {
  name: string
  initialState: T
  reducers: Record<string, (state: T, payload?: any) => T>
  actions: Record<string, (...args: any[]) => any>
}

interface StoreState {
  [key: string]: any
}

export class KilatStore {
  private state: StoreState = {}
  private listeners: Map<string, Set<StateListener>> = new Map()
  private slices: Map<string, StateSlice> = new Map()
  private config: KilatConfig
  private isHydrated = false
  
  constructor(config: KilatConfig) {
    this.config = config
    
    // Initialize from SSR state if available
    if (typeof window !== 'undefined' && (window as any).__KILAT_STATE__) {
      this.state = (window as any).__KILAT_STATE__
      this.isHydrated = true
      console.log('🔄 KilatState: Hydrated from SSR state')
    }
    
    // Setup persistence if enabled
    if (this.config.state.persistence !== 'none') {
      this.setupPersistence()
    }
  }
  
  /**
   * Create a state slice
   */
  createSlice<T>(slice: StateSlice<T>): {
    actions: Record<string, (...args: any[]) => void>
    selectors: {
      getState: () => T
      subscribe: (listener: StateListener<T>) => () => void
    }
  } {
    // Initialize slice state
    if (!(slice.name in this.state)) {
      this.state[slice.name] = slice.initialState
    }
    
    // Store slice definition
    this.slices.set(slice.name, slice)
    
    // Create bound actions
    const boundActions: Record<string, (...args: any[]) => void> = {}
    
    for (const [actionName, actionCreator] of Object.entries(slice.actions)) {
      boundActions[actionName] = (...args: any[]) => {
        const payload = actionCreator(...args)
        this.dispatch(slice.name, actionName, payload)
      }
    }
    
    // Create selectors
    const selectors = {
      getState: () => this.getState(slice.name) as T,
      subscribe: (listener: StateListener<T>) => this.subscribe(slice.name, listener)
    }
    
    console.log(`🗄️ KilatState: Created slice '${slice.name}'`)
    
    return { actions: boundActions, selectors }
  }
  
  /**
   * Dispatch an action
   */
  dispatch(sliceName: string, actionName: string, payload?: any): void {
    const slice = this.slices.get(sliceName)
    if (!slice) {
      console.error(`KilatState: Slice '${sliceName}' not found`)
      return
    }
    
    const reducer = slice.reducers[actionName]
    if (!reducer) {
      console.error(`KilatState: Action '${actionName}' not found in slice '${sliceName}'`)
      return
    }
    
    const oldState = this.state[sliceName]
    const newState = reducer(oldState, payload)
    
    // Update state
    this.state[sliceName] = newState
    
    // Notify listeners
    this.notifyListeners(sliceName, newState, oldState)
    
    // Persist state if enabled
    this.persistState()
    
    console.log(`🔄 KilatState: Dispatched ${sliceName}/${actionName}`, payload)
  }
  
  /**
   * Get state for a slice
   */
  getState<T = any>(sliceName: string): T {
    return this.state[sliceName] as T
  }
  
  /**
   * Get entire store state
   */
  getAllState(): StoreState {
    return { ...this.state }
  }
  
  /**
   * Subscribe to state changes
   */
  subscribe<T = any>(sliceName: string, listener: StateListener<T>): () => void {
    if (!this.listeners.has(sliceName)) {
      this.listeners.set(sliceName, new Set())
    }
    
    this.listeners.get(sliceName)!.add(listener)
    
    // Return unsubscribe function
    return () => {
      const sliceListeners = this.listeners.get(sliceName)
      if (sliceListeners) {
        sliceListeners.delete(listener)
      }
    }
  }
  
  /**
   * Notify listeners of state changes
   */
  private notifyListeners(sliceName: string, newState: any, oldState: any): void {
    const sliceListeners = this.listeners.get(sliceName)
    if (sliceListeners) {
      for (const listener of sliceListeners) {
        try {
          listener(newState, oldState)
        } catch (error) {
          console.error('Error in state listener:', error)
        }
      }
    }
  }
  
  /**
   * Setup state persistence
   */
  private setupPersistence(): void {
    if (typeof window === 'undefined') return
    
    const storageKey = 'kilat-state'
    const storage = this.config.state.persistence === 'localStorage' 
      ? localStorage 
      : sessionStorage
    
    // Load persisted state
    try {
      const persistedState = storage.getItem(storageKey)
      if (persistedState && !this.isHydrated) {
        const parsed = JSON.parse(persistedState)
        this.state = { ...this.state, ...parsed }
        console.log('💾 KilatState: Loaded persisted state')
      }
    } catch (error) {
      console.error('Error loading persisted state:', error)
    }
  }
  
  /**
   * Persist current state
   */
  private persistState(): void {
    if (typeof window === 'undefined' || this.config.state.persistence === 'none') {
      return
    }
    
    const storageKey = 'kilat-state'
    const storage = this.config.state.persistence === 'localStorage' 
      ? localStorage 
      : sessionStorage
    
    try {
      storage.setItem(storageKey, JSON.stringify(this.state))
    } catch (error) {
      console.error('Error persisting state:', error)
    }
  }
  
  /**
   * Reset state to initial values
   */
  reset(sliceName?: string): void {
    if (sliceName) {
      const slice = this.slices.get(sliceName)
      if (slice) {
        const oldState = this.state[sliceName]
        this.state[sliceName] = slice.initialState
        this.notifyListeners(sliceName, slice.initialState, oldState)
        console.log(`🔄 KilatState: Reset slice '${sliceName}'`)
      }
    } else {
      // Reset all slices
      for (const [name, slice] of this.slices) {
        const oldState = this.state[name]
        this.state[name] = slice.initialState
        this.notifyListeners(name, slice.initialState, oldState)
      }
      console.log('🔄 KilatState: Reset all slices')
    }
    
    this.persistState()
  }
  
  /**
   * Get SSR state for hydration
   */
  getSSRState(): StoreState {
    return this.getAllState()
  }
  
  /**
   * Check if store is hydrated
   */
  isStoreHydrated(): boolean {
    return this.isHydrated
  }
  
  /**
   * Get store statistics
   */
  getStats(): {
    sliceCount: number
    listenerCount: number
    stateSize: number
    isHydrated: boolean
  } {
    let totalListeners = 0
    for (const listeners of this.listeners.values()) {
      totalListeners += listeners.size
    }
    
    const stateSize = JSON.stringify(this.state).length
    
    return {
      sliceCount: this.slices.size,
      listenerCount: totalListeners,
      stateSize,
      isHydrated: this.isHydrated
    }
  }
}

// Global store instance
let globalStore: KilatStore | null = null

/**
 * Get or create global store instance
 */
export function getStore(config?: KilatConfig): KilatStore {
  if (!globalStore && config) {
    globalStore = new KilatStore(config)
  }
  
  if (!globalStore) {
    throw new Error('KilatStore not initialized. Call getStore(config) first.')
  }
  
  return globalStore
}

/**
 * Create a state slice (convenience function)
 */
export function createSlice<T>(slice: StateSlice<T>) {
  const store = getStore()
  return store.createSlice(slice)
}
