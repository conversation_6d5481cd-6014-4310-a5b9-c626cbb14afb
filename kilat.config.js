/**
 * Kilat.js Configuration
 * Framework configuration for SpeedRun runtime and all core modules
 */

/** @type {import('./core/kilatcore/types').KilatConfig} */
export default {
  // 🚀 SpeedRun Runtime Configuration
  runtime: {
    engine: 'bun', // 'bun' | 'node' | 'auto'
    fallback: 'node', // fallback when primary engine unavailable
    port: 3000,
    host: 'localhost',
    https: false,
  },

  // 🔀 App Mapping Configuration
  apps: {
    dir: './apps',
    extensions: ['.tsx', '.ts', '.jsx', '.js'],
    layouts: {
      global: './apps/layout.tsx',
      nested: true, // support nested layouts
    },
    api: {
      dir: './apps/api',
      prefix: '/api',
    },
  },

  // 🏗️ Build Configuration (KilatPack)
  build: {
    outDir: './.kilat/dist',
    target: 'es2022',
    minify: true,
    sourcemap: true,
    splitting: true,
    treeshaking: true,
    bundleAnalyzer: false,
  },

  // 🔌 Plugin Configuration
  plugins: {
    dir: './core/kilatplugin',
    autoLoad: true,
    devPlugins: [], // plugins only for development
    buildPlugins: [], // plugins only for build
  },

  // 🎨 CSS & Theme Configuration
  css: {
    framework: 'tailwind', // 'tailwind' | 'vanilla' | 'styled'
    themes: {
      default: 'glow',
      available: ['glow', 'cyber', 'pastel', 'retro'],
    },
    animations: {
      enabled: true,
      preset: 'smooth', // 'smooth' | 'bouncy' | 'sharp'
    },
  },

  // 🗄️ State Management
  state: {
    ssr: true,
    hydration: 'partial', // 'full' | 'partial' | 'islands'
    persistence: 'localStorage', // 'localStorage' | 'sessionStorage' | 'none'
  },

  // 🖼️ Image Optimization
  images: {
    domains: [],
    formats: ['webp', 'avif'],
    sizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    quality: 75,
  },

  // 🔒 Security & Middleware
  middleware: {
    cors: {
      enabled: true,
      origin: '*',
    },
    rateLimit: {
      enabled: false,
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    },
    auth: {
      providers: [], // auth providers configuration
    },
  },

  // 📊 Analytics & SEO
  meta: {
    siteName: 'Kilat.js App',
    defaultTitle: 'Welcome to Kilat.js',
    titleTemplate: '%s | Kilat.js',
    description: 'Built with Kilat.js - Lightning fast fullstack framework',
    openGraph: {
      type: 'website',
      locale: 'en_US',
    },
  },

  // 🧪 Development Configuration
  dev: {
    hmr: true, // hot module replacement
    overlay: true, // error overlay
    port: 3000,
    open: true, // auto open browser
    https: false,
  },

  // 📦 Export Configuration (SSG)
  export: {
    outDir: './.kilat/static',
    trailingSlash: false,
    generateSitemap: true,
    generateRobots: true,
  },

  // 🔧 Advanced Configuration
  experimental: {
    turbo: false, // experimental turbo mode
    edgeRuntime: false, // edge runtime support
    serverComponents: true, // server components
  },
}
