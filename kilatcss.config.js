/**
 * KilatCSS Configuration
 * Tailwind CSS configuration with Kilat.js themes and animations
 */

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './apps/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './core/**/*.{js,ts,jsx,tsx}',
  ],
  
  theme: {
    extend: {
      // 🎨 Kilat.js Theme Colors
      colors: {
        // Glow Theme (Default)
        glow: {
          primary: '#3b82f6',
          secondary: '#8b5cf6',
          accent: '#06b6d4',
          background: '#0f172a',
          surface: '#1e293b',
          text: '#f8fafc',
          muted: '#64748b',
        },
        
        // Cyber Theme
        cyber: {
          primary: '#00ff88',
          secondary: '#ff0080',
          accent: '#00d4ff',
          background: '#0a0a0a',
          surface: '#1a1a1a',
          text: '#ffffff',
          muted: '#666666',
        },
        
        // Pastel Theme
        pastel: {
          primary: '#fbbf24',
          secondary: '#f472b6',
          accent: '#34d399',
          background: '#fef7ff',
          surface: '#ffffff',
          text: '#1f2937',
          muted: '#9ca3af',
        },
        
        // Retro Theme
        retro: {
          primary: '#f59e0b',
          secondary: '#ef4444',
          accent: '#10b981',
          background: '#292524',
          surface: '#44403c',
          text: '#fbbf24',
          muted: '#a8a29e',
        },
      },
      
      // 🌟 Kilat Animations
      animation: {
        'kilat-fade-in': 'kilatFadeIn 0.5s ease-out',
        'kilat-slide-up': 'kilatSlideUp 0.6s ease-out',
        'kilat-slide-down': 'kilatSlideDown 0.6s ease-out',
        'kilat-scale-in': 'kilatScaleIn 0.4s ease-out',
        'kilat-bounce-in': 'kilatBounceIn 0.8s ease-out',
        'kilat-glow': 'kilatGlow 2s ease-in-out infinite alternate',
        'kilat-pulse': 'kilatPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'kilat-spin-slow': 'spin 3s linear infinite',
        'kilat-float': 'kilatFloat 3s ease-in-out infinite',
      },
      
      keyframes: {
        kilatFadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        kilatSlideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        kilatSlideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        kilatScaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        kilatBounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        kilatGlow: {
          '0%': { boxShadow: '0 0 5px currentColor' },
          '100%': { boxShadow: '0 0 20px currentColor, 0 0 30px currentColor' },
        },
        kilatPulse: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.5' },
        },
        kilatFloat: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
      
      // 🔤 Typography
      fontFamily: {
        'kilat-sans': ['Inter', 'system-ui', 'sans-serif'],
        'kilat-mono': ['JetBrains Mono', 'Consolas', 'monospace'],
        'kilat-display': ['Poppins', 'system-ui', 'sans-serif'],
      },
      
      // 📐 Spacing & Sizing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      
      // 🎭 Effects
      backdropBlur: {
        'kilat': '12px',
      },
      
      boxShadow: {
        'kilat-glow': '0 0 20px rgba(59, 130, 246, 0.5)',
        'kilat-cyber': '0 0 20px rgba(0, 255, 136, 0.5)',
        'kilat-soft': '0 4px 20px rgba(0, 0, 0, 0.1)',
      },
    },
  },
  
  plugins: [
    // Custom Kilat.js utilities
    function({ addUtilities, theme }) {
      const newUtilities = {
        '.kilat-glow': {
          boxShadow: theme('boxShadow.kilat-glow'),
        },
        '.kilat-cyber-glow': {
          boxShadow: theme('boxShadow.kilat-cyber'),
        },
        '.kilat-glass': {
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(12px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.kilat-gradient-text': {
          background: 'linear-gradient(45deg, #3b82f6, #8b5cf6)',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
      }
      
      addUtilities(newUtilities)
    },
  ],
}
