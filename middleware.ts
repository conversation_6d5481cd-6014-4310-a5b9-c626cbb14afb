/**
 * Global Middleware - Kilat.js
 * Handles authentication, RBAC, CORS, rate limiting, and other cross-cutting concerns
 */

import { NextRequest, NextResponse } from 'next/server'
import { isAuthenticated } from '@/core/kilatlib/auth'
import { rateLimit } from '@/core/kilatlib/rate-limit'
import { cors } from '@/core/kilatlib/cors'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Apply CORS headers
  const response = cors(request)
  
  // Rate limiting for API routes
  if (pathname.startsWith('/api/')) {
    const rateLimitResult = await rateLimit(request)
    if (!rateLimitResult.success) {
      return new Response('Too Many Requests', { 
        status: 429,
        headers: response.headers 
      })
    }
  }
  
  // Authentication for protected routes
  const protectedRoutes = ['/dashboard']
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  )
  
  if (isProtectedRoute) {
    const session = await isAuthenticated(request)
    if (!session) {
      return NextResponse.redirect(new URL('/login', request.url))
    }
    
    // Add user info to headers for downstream consumption
    response.headers.set('x-user-id', session.userId)
    response.headers.set('x-user-role', session.role)
  }
  
  // API authentication
  if (pathname.startsWith('/api/') && !pathname.startsWith('/api/auth/')) {
    const apiKey = request.headers.get('x-api-key')
    const authHeader = request.headers.get('authorization')
    
    if (!apiKey && !authHeader) {
      return new Response('Unauthorized', { 
        status: 401,
        headers: response.headers 
      })
    }
  }
  
  // Security headers
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin')
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
  )
  
  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
