# ⚡ Kilat.js

> Modern fullstack framework built from scratch - standalone and lightning fast

Kilat.js adalah framework fullstack modern yang dibangun dari nol tanpa mengandalkan framework lain. Menggabungkan kekuatan Bun.js + Node.js sebagai runtime utama dengan sistem file-based routing yang canggih.

## 🚀 Fitur Utama

- **⚡ Lightning Fast**: Runtime SpeedRun dengan Bun.js + Node.js fallback
- **🏗️ Standalone**: Tidak bergantung pada Express, Vite, Next.js, atau framework lain
- **🔀 App Mapping**: File-based routing modern dengan layout otomatis
- **📦 KilatPack**: Build engine internal super cepat
- **🔌 Plugin System**: Sistem plugin modular mirip Vite
- **🎨 Built-in Themes**: KilatCSS dengan preset tema (glow, cyber, pastel, retro)
- **🌟 Animations**: KilatAnim dengan preset animasi dan Three.js support
- **🗄️ Full Stack**: ORM, State Management, API handlers, dan lebih banyak lagi

## 📁 Struktur Proyek

```
kilat/
├── apps/                      # 🔀 File-based router (Apps Mapping)
│   ├── layout.tsx            # Global layout
│   ├── page.tsx              # Homepage (/)
│   ├── about/page.tsx        # /about
│   ├── dashboard/            # /dashboard/*
│   └── api/                  # 🛠️ Native API handler
├── components/               # 💠 Global UI components
├── core/                     # ⚙️ Kernel modular internal
│   ├── kilatcore/            # Lifecycle & runtime
│   ├── kilatpack/            # Build engine mandiri
│   ├── kilatplugin/          # Plugin loader
│   ├── kilatservice/         # Business logic
│   ├── kilatcss/             # Tailwind + theme system
│   ├── kilatanim/            # Preset animasi
│   └── ...                   # Modul lainnya
├── public/                   # 🖼️ Static files
├── kilat.config.js           # ⚙️ Konfigurasi utama
└── kilat.cli.ts              # 🔧 CLI generator
```

## 🛠️ Quick Start

```bash
# Install Bun (jika belum ada)
curl -fsSL https://bun.sh/install | bash

# Clone atau create project
git clone https://github.com/kilat-js/kilat.js
cd kilat.js

# Install dependencies
bun install

# Start development server
bun dev

# Build for production
bun build

# Export static site
bun export
```

## 🎨 Tema & Styling

Kilat.js hadir dengan sistem tema built-in:

```tsx
// Ganti tema di kilat.config.js
export default {
  css: {
    themes: {
      default: 'glow', // 'glow' | 'cyber' | 'pastel' | 'retro'
    }
  }
}
```

### Tema yang Tersedia:
- **Glow**: Modern dark theme dengan accent biru
- **Cyber**: Futuristic theme dengan neon hijau/pink
- **Pastel**: Light theme dengan warna-warna soft
- **Retro**: Vintage theme dengan warna hangat

## 🔧 CLI Commands

```bash
# Development
bun kilat dev              # Start dev server
bun kilat build            # Build for production
bun kilat start            # Start production server

# Generators
bun kilat generate page about        # Generate new page
bun kilat generate api users         # Generate API route
bun kilat generate component Button  # Generate component
bun kilat generate service auth      # Generate service

# Utilities
bun kilat export           # Export to static files
bun kilat upgrade          # Upgrade Kilat.js version
```

## 📖 Dokumentasi

### File-based Routing

```
apps/
├── page.tsx              → /
├── about/page.tsx        → /about
├── blog/
│   ├── page.tsx          → /blog
│   └── [slug]/page.tsx   → /blog/:slug
└── api/
    └── users/route.ts    → /api/users
```

### Layouts

```tsx
// apps/layout.tsx - Global layout
export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <nav>...</nav>
        {children}
        <footer>...</footer>
      </body>
    </html>
  )
}

// apps/dashboard/layout.tsx - Nested layout
export default function DashboardLayout({ children }) {
  return (
    <div className="dashboard">
      <sidebar>...</sidebar>
      <main>{children}</main>
    </div>
  )
}
```

### API Routes

```tsx
// apps/api/users/route.ts
export async function GET(request: Request) {
  return Response.json({ users: [] })
}

export async function POST(request: Request) {
  const body = await request.json()
  // Handle POST request
  return Response.json({ success: true })
}
```

## 🧩 Plugin System

```tsx
// core/kilatplugin/my-plugin.ts
export default function myPlugin() {
  return {
    name: 'my-plugin',
    setup(build) {
      // Plugin logic
    }
  }
}

// kilat.config.js
export default {
  plugins: {
    buildPlugins: ['my-plugin']
  }
}
```

## 🗄️ Services

```tsx
// core/kilatservice/user.service.ts
export class UserService {
  async getUsers() {
    // Business logic
  }
  
  async createUser(data: any) {
    // Business logic
  }
}

// Gunakan di page atau API
import { UserService } from '@/core/kilatservice/user.service'

const userService = new UserService()
const users = await userService.getUsers()
```

## 🎯 Roadmap

- [x] ✅ Struktur dasar App Mapping
- [x] ✅ Konfigurasi dan setup
- [ ] 🔄 SpeedRun Runtime implementation
- [ ] 🔄 KilatPack build engine
- [ ] 🔄 Plugin system
- [ ] 🔄 Core modules (CSS, Anim, State, dll)
- [ ] 🔄 CLI tools
- [ ] 🔄 Testing & documentation

## 🤝 Contributing

Kontribusi sangat diterima! Silakan baca [CONTRIBUTING.md](CONTRIBUTING.md) untuk panduan.

## 📄 License

MIT License - lihat [LICENSE](LICENSE) untuk detail.

---

**Dibuat dengan ⚡ oleh Tim Kilat.js**
