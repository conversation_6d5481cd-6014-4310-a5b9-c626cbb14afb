# 📁 Struktur Kilat.js Framework

Dokumentasi lengkap struktur file dan direktori framework Kilat.js.

## 🏗️ Struktur Utama

```
kilat.js/
├── 📁 apps/                      # 🔀 File-based routing (App Mapping)
├── 📁 components/               # 💠 Global UI components
├── 📁 core/                     # ⚙️ Kernel modular internal
├── 📁 public/                   # 🖼️ Static assets
├── 📁 .kilat/                   # Runtime metadata (auto-generated)
├── 📄 kilat.config.js           # ⚙️ Konfigurasi utama framework
├── 📄 kilatcss.config.js        # 🎨 Konfigurasi tema & styling
├── 📄 middleware.ts             # 🛡️ Global middleware
├── 📄 cli.ts                    # 🔧 CLI tool
├── 📄 package.json              # 📦 Dependencies & scripts
├── 📄 tsconfig.json             # 🔧 TypeScript configuration
└── 📄 readme.md                 # 📖 Dokumentasi utama
```

## 🔀 Apps Directory (File-based Routing)

```
apps/
├── 📄 layout.tsx               # Global layout untuk semua halaman
├── 📄 page.tsx                 # Homepage (route: /)
├── 📁 about/
│   └── 📄 page.tsx             # About page (route: /about)
├── 📁 dashboard/
│   ├── 📄 layout.tsx           # Nested layout untuk dashboard
│   ├── 📄 page.tsx             # Dashboard home (route: /dashboard)
│   ├── 📁 settings/
│   │   └── 📄 page.tsx         # Settings (route: /dashboard/settings)
│   └── 📁 users/
│       └── 📄 page.tsx         # Users (route: /dashboard/users)
└── 📁 api/                     # 🛠️ Native API routes
    ├── 📁 auth/
    │   └── 📄 route.ts         # Auth endpoints (POST /api/auth)
    └── 📁 users/
        └── 📄 route.ts         # Users CRUD (GET/POST/PUT/DELETE /api/users)
```

### Konvensi Routing:
- `page.tsx` = Halaman utama untuk route tersebut
- `layout.tsx` = Layout wrapper untuk route dan sub-routes
- `route.ts` = API endpoint handler
- `[param]/` = Dynamic route parameter
- `(group)/` = Route grouping tanpa mempengaruhi URL

## 💠 Components Directory

```
components/
├── 📁 ui/                      # Komponen UI dasar
│   ├── 📄 button.tsx           # KilatButton component
│   ├── 📄 card.tsx             # KilatCard component
│   ├── 📄 input.tsx            # Form inputs
│   └── 📄 modal.tsx            # Modal dialogs
├── 📁 layout/                  # Layout components
│   ├── 📄 header.tsx           # Global header
│   ├── 📄 footer.tsx           # Global footer
│   └── 📄 sidebar.tsx          # Sidebar navigation
└── 📁 shared/                  # Shared business components
    ├── 📄 user-avatar.tsx      # User avatar component
    └── 📄 data-table.tsx       # Reusable data table
```

## ⚙️ Core Directory (Framework Kernel)

```
core/
├── 📁 kilatcore/               # Runtime & lifecycle management
│   ├── 📄 speedrun.ts          # SpeedRun runtime engine
│   ├── 📄 router.ts            # File-based router
│   ├── 📄 types.ts             # TypeScript definitions
│   └── 📄 middleware.ts        # Middleware processor
├── 📁 kilatpack/               # Build engine mandiri
│   ├── 📄 builder.ts           # Main build orchestrator
│   ├── 📄 bundler.ts           # Code bundling
│   ├── 📄 optimizer.ts         # Asset optimization
│   └── 📄 transformer.ts       # Code transformation
├── 📁 kilatplugin/             # Plugin system
│   ├── 📄 manager.ts           # Plugin manager
│   ├── 📄 loader.ts            # Plugin loader
│   └── 📄 types.ts             # Plugin type definitions
├── 📁 kilatservice/            # Business logic layer
│   ├── 📄 auth.service.ts      # Authentication service
│   ├── 📄 user.service.ts      # User management service
│   └── 📄 base.service.ts      # Base service class
├── 📁 kilatorm/                # ORM ringan + auto CRUD
│   ├── 📄 connection.ts        # Database connection
│   ├── 📄 model.ts             # Base model class
│   ├── 📄 query.ts             # Query builder
│   └── 📄 migrations.ts        # Database migrations
├── 📁 kilatcss/                # Tailwind + theme system
│   ├── 📄 globals.css          # Global CSS styles
│   ├── 📄 processor.ts         # CSS processor
│   └── 📁 themes/              # Theme definitions
│       ├── 📄 glow.ts          # Glow theme (default)
│       ├── 📄 cyber.ts         # Cyber theme
│       ├── 📄 pastel.ts        # Pastel theme
│       └── 📄 retro.ts         # Retro theme
├── 📁 kilatanim/               # Animation system
│   ├── 📄 presets.ts           # Animation presets
│   ├── 📄 three.ts             # Three.js integration
│   └── 📄 transitions.ts       # Page transitions
├── 📁 kilatstate/              # Global state management
│   ├── 📄 store.ts             # State store
│   ├── 📄 ssr.ts               # SSR state hydration
│   └── 📄 persistence.ts       # State persistence
├── 📁 kilatmeta/               # SEO & metadata
│   ├── 📄 head.ts              # HTML head management
│   ├── 📄 seo.ts               # SEO optimization
│   ├── 📄 opengraph.ts         # OpenGraph tags
│   └── 📄 jsonld.ts            # JSON-LD structured data
├── 📁 kilatimage/              # Image optimization
│   ├── 📄 optimizer.ts         # Image optimizer
│   ├── 📄 formats.ts           # Format conversion
│   └── 📄 lazy.ts              # Lazy loading
├── 📁 kilatsocket/             # WebSocket native
│   ├── 📄 server.ts            # WebSocket server
│   ├── 📄 client.ts            # WebSocket client
│   └── 📄 rooms.ts             # Room management
├── 📁 kilatcache/              # Caching system
│   ├── 📄 memory.ts            # In-memory cache
│   ├── 📄 redis.ts             # Redis integration
│   ├── 📄 isr.ts               # Incremental Static Regeneration
│   └── 📄 swr.ts               # Stale-While-Revalidate
├── 📁 kilatffmpeg/             # Media processing
│   ├── 📄 video.ts             # Video processing
│   ├── 📄 audio.ts             # Audio processing
│   └── 📄 thumbnails.ts        # Thumbnail generation
├── 📁 kilatfonts/              # Font management
│   ├── 📄 loader.ts            # Font loader
│   ├── 📄 optimizer.ts         # Font optimization
│   └── 📄 presets.ts           # Font presets
├── 📁 kilatlib/                # Utilities & helpers
│   ├── 📄 utils.ts             # Common utilities
│   ├── 📄 validator.ts         # Data validation
│   ├── 📄 constants.ts         # Framework constants
│   ├── 📄 generator.ts         # Code generators
│   ├── 📄 auth.ts              # Auth helpers
│   ├── 📄 rate-limit.ts        # Rate limiting
│   └── 📄 cors.ts              # CORS handling
├── 📁 kilatupgrade/            # Auto updater
│   ├── 📄 checker.ts           # Version checker
│   ├── 📄 downloader.ts        # Update downloader
│   └── 📄 installer.ts         # Update installer
├── 📁 kilaterror/              # Error handling
│   ├── 📄 overlay.ts           # Development error overlay
│   ├── 📄 logger.ts            # Error logging
│   └── 📄 reporter.ts          # Error reporting
└── 📁 shared/                  # Common helpers
    ├── 📄 types.ts             # Shared type definitions
    ├── 📄 constants.ts         # Shared constants
    └── 📄 helpers.ts           # Shared helper functions
```

## 🖼️ Public Directory

```
public/
├── 📄 favicon.ico              # Website favicon
├── 📄 logo.png                 # Kilat.js logo
├── 📄 manifest.json            # PWA manifest
├── 📁 images/                  # Static images
├── 📁 fonts/                   # Custom fonts
└── 📁 icons/                   # Icon assets
```

## 🔧 Configuration Files

### kilat.config.js
Konfigurasi utama framework meliputi:
- Runtime settings (Bun/Node)
- Build configuration
- Plugin settings
- Development options
- Export settings

### kilatcss.config.js
Konfigurasi Tailwind CSS dengan:
- Theme definitions
- Custom utilities
- Animation presets
- Responsive breakpoints

### middleware.ts
Global middleware untuk:
- Authentication
- CORS handling
- Rate limiting
- Security headers
- Request logging

## 🚀 Generated Files (.kilat/)

```
.kilat/
├── 📁 build/                   # Build artifacts
├── 📁 dist/                    # Production build
├── 📁 static/                  # Static export
├── 📁 cache/                   # Build cache
├── 📁 types/                   # Generated types
└── 📄 metadata.json            # Runtime metadata
```

## 📋 File Naming Conventions

- **Pages**: `page.tsx` untuk halaman utama
- **Layouts**: `layout.tsx` untuk layout wrapper
- **API Routes**: `route.ts` untuk endpoint handlers
- **Components**: PascalCase (e.g., `UserCard.tsx`)
- **Services**: kebab-case dengan `.service.ts` suffix
- **Types**: kebab-case dengan `.types.ts` suffix
- **Utils**: kebab-case dengan `.utils.ts` suffix

## 🔄 Import Paths

Framework menggunakan path mapping untuk import yang bersih:

```typescript
// Apps
import HomePage from '@/apps/page'

// Components
import { KilatButton } from '@/components/ui/button'

// Core modules
import { SpeedRunRuntime } from '@/core/kilatcore/speedrun'
import { UserService } from '@/core/kilatservice/user.service'

// Utils
import { cn } from '@/core/kilatlib/utils'

// Public assets
import logo from '@/public/logo.png'
```

## 📝 Notes

- Semua file TypeScript menggunakan strict mode
- Components menggunakan React dengan TypeScript
- API routes menggunakan Web API standard (Request/Response)
- Styling menggunakan Tailwind CSS dengan custom theme system
- State management terintegrasi dengan SSR/SSG support
