{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next/typescript-plugin"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/apps/*": ["./apps/*"], "@/components/*": ["./components/*"], "@/core/*": ["./core/*"], "@/public/*": ["./public/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".kilat/types/**/*.ts"], "exclude": ["node_modules", ".kilat/build", ".kilat/dist"]}